<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">

<meta name="x-apple-disable-message-reformatting">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

  <title></title>

  <style type="text/css">
    @import url('https://fonts.googleapis.com/css?family=Open+Sans:400,600');
    body, table, td, a {
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }
    table, td {
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
        } 
 
    table {
      border-collapse: collapse !important;
    }
    body {
      height: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      width: 100% !important;
      background-color: #f6f6f6;
      font-family: 'Open Sans', sans-serif;
      font-size: 15px;
      color:#2d2d2d;
    }
    .button {
      padding: 10px 10px;
      background-color: #ff0e0e !important;
      background-image: linear-gradient(#ff1818, #c00000) !important;
      color: #fff !important;
    }
    a { color : #005897;
      text-decoration: none;
    }
    .content-shift {
      margin-left: 10px;
      padding: 2px;
    }
    .heading {
      text-align: center;
      border-right: 1px solid #ccc;
      padding: 12px;
      color: #fff;
      font-weight: 400;
    }

    .next-heading {
        border: 1px solid #005897;
        background-color: #005897;
        color: #fff;
        padding: 10px;
        font-size: 20px;
        text-align: center;
        margin: 0;
    }

    .bordered-table {
      border: 1px solid #f6f6f6;
      /*text-align: center;*/
      font-size: 15px;
      padding: 10px
    }


    a[x-apple-data-detectors] {
      color: inherit !important;
      text-decoration: none !important;
      font-size: inherit !important;
      font-family: inherit !important;
      font-weight: inherit !important;
      line-height: inherit !important;
    }

    .sub-headings {
      color: #192e77;
      background-color: #e7e7e7;
      padding: 10px;
      margin: 0 auto;
      font-size: 18px;
    }

    .header-url {
        background-color: none;
    }
    .main-body-table {
      width: 700px;
      margin: 0 auto;
    }

    ul li {margin-right:15px; margin-top: 10px;}
    @media only screen and (max-width: 1900px) and (min-width: 600px) {

    .content-right-shift {
        display:none;
    }
   
    .main-body-table {
      width: 700px !important;
      margin: 0 auto;
    }
    .header-url {
        background-color: #e3e3e3;
      font-size: 15px !important;
    }
    .intro-para p span {line-height: 20px; margin-left: 10px; font-size: 16px !important; padding: 2px;}
    .price-shift {
        margin-left: 28px !important;
      font-weight:600;
    }
    .mobile-responsive {display:none !important;}
    .main-body-table {
      position: relative;
      width: 100%;
      margin: 0 auto;
    }
    
    .responsive .bordered-table span {
      margin-left: 0px;
      line-height: 20px;
    }
    .responsive .sub-table td {
      padding: 10px;
      font-size: 15px;
    }
    .responsive > table .sub-table td:before {
      content: attr(data-label);
      float: left;
      font-weight: bold;
      text-transform: uppercase;
      color: #5a5a5a;
    }
    .responsive .sub-table span {
      margin-left: -9px;
      line-height: 20px;
    }
    .responsive > table {
      border: 0;
    }
    .responsive > table thead {
      display: none;
    }
    .responsive > table tr {
      display: block;
      margin-bottom: 5px;
    }
    .responsive > table td {
      /*border-bottom: 1px dotted #ccc;*/
      /*display: block;*/
      font-size: 15px;
      /*text-align: left;*/
      padding: 2px;
    }
    .responsive > table td:last-child {
      border-bottom: 1px dotted #ccc;
    }
    .responsive > table td:before {
      content: attr(data-label);
      float: left;
      font-weight: bold;
      text-transform: uppercase;
      color: #24427e;
    }
    .wrapper {
      width: 100% !important;
      max-width: 100% !important;
    }

    .responsive-table {
      width: 100% !important;
    }
    .responsive tr {
        border-top: 1px solid #c5c5c5;
      border-bottom: 1px solid #c5c5c5;
    }
    .table-responsive {
      display: block;
      overflow-x: auto;
      width: 100%;
    }
    .bordered-table {
      border: none;
      /*text-align: center;*/
      font-size: 15px;
      padding: 10px
    }
   
    .vertical-alignment-td-1{ width:35%;}
    .vertical-alignment-td-2{ width:55%;}
    }

      
    @media only screen and (max-width: 767px) and (min-width: 240px) {
    .content-right-shift {
              display:block;
        text-align: right;
        font-weight: 600;
        margin-right: 12px;
    }
          .email-button {
        display: none;
    }
          .button-whatsapp {
        padding: 10px 10px;
        background-color: #4df50f !important;
        background-image: linear-gradient(#0cc807, #07650f) !important;
        color: #fff !important;
    }

    .header-url {
        background-color: #e3e3e3;
      font-size: 15px !important;
    }
    .price-shift {
      font-weight:600;
    }
    .intro-para p span {line-height: 20px !important; margin-left: 10px !important; padding: 2px !important; font-size: 16px !important;}
    .mobile-responsive {display:none !important;}
    .main-body-table {
      position: relative;
      width: 100%;
      margin: 0 auto;
      background-color: #fff;
    }
    .sub-table tr {
        border: none !important;
    }
    .sub-table td {
        border-bottom: none;
        border-right: none;
    }
    .responsive .bordered-table span {
      margin-left: 12px;
      line-height: 20px;
    }
    .responsive .sub-table td {
      padding: 4px;
    }
    .responsive > table .sub-table td:before {
      content: attr(data-label);
      float: left;
      font-weight: bold;
      text-transform: uppercase;
      color: #5a5a5a;
    }
    .responsive .sub-table span {
      line-height: 20px;
    }
    .responsive > table {
      border: 0;
    }
    .responsive > table thead {
      display: none;
    }
    .responsive > table tr {
      display: block;
      margin-bottom: 5px;
    }
    .responsive > table td {
      border-bottom: 1px dotted #ccc;
      display: block;
      font-size: 14px;
      /*text-align: left;*/
      padding: 20px 12px 35px 0px;
    }
    .responsive > table td:last-child {
      border-bottom: 0;
    }
    .responsive > table td:before {
      content: attr(data-label);
      float: left;
      font-weight: bold;
      text-transform: uppercase;
      color: #24427e;
    }
    .wrapper {
      width: 100% !important;
      max-width: 100% !important;
    }

    .responsive-table {
      width: 100% !important;
    }
    .responsive tr {
        border-top: 2px solid #c5c5c5;
      border-bottom: 2px solid #c5c5c5;
    }
    .table-responsive {
      display: block;
      overflow-x: auto;
      width: 100%;
      border:none;
    }
    .bordered-table {
      border: none;
      /*text-align: center;*/
      font-size: 15px;
      padding: 10px
    }
  
 
   
    }
  </style>
</head>
<body class="wrapper" style="margin: 0 !important; padding: 0 !important;">
<div class="message"></div>
<input type="hidden" name="_token" value="{{ csrf_token() }}">

<div class="main-body-table">
  <table class="wrapper" style="background-color: #fff;width: 700px;" align="center">
    <tbody>
      <tr style="width: 100%; background-color: #005897; text-align:center;">
         <td class="wrapper-inner"> 
          <p style="color: #fff;text-align: center;font-size: 28px;">Qureka Admin Panel</p>
          <p style="color: #fff;text-align: center;font-size: 16px;margin-top: 0px;"></p>
         </td>
      </tr>

      <tr>
        <td>
          <table>
            <tbody>
              <tr>
                <td style="font-size:16px;"></td>
              </tr>
              <tr>
                <td class="intro-para"><p style="margin-top: 10px; padding: 2px;">
                  <span style="color:#333333; display:block; font-size:12px; margin-bottom:10px;">
                    Dear Admin,
                  </span></p>
                    
                  <p style="padding:2px"><span style="color:#333333;display:block;text-align:left;font-size:12px;margin-bottom:10px">Domain health report except running website</span></p>
                
                </td>
              </tr>
            </tbody>
          </table>
          <table class="spacer">
            <tbody>
              <tr>
                <td style="font-size:16px;line-height:16px;" height="16px">&nbsp;</td>
              </tr>
            </tbody>
          </table></td>
      </tr>
      
              
            
          
      
    </tbody>
  </table>
  <div class="table-responsive responsive">
    <table class="wrapper" style="background-color: #fff; width: 1000px;overflow: scroll;" align="center">
       <tr class="main-table-tr">
            <td class="bordered-table" style="min-width:200px;">
              <strong> Domain Name</strong>
            </td>
            <td class="bordered-table">
               <strong>Status</strong>
            </td>
        </tr>
      <tbody>
      @foreach ($data['domain_info'] as $val)
        <tr class="main-table-tr">
            <td class="bordered-table" style="min-width:200px;">
               {{$val['domain_name']}}
            </td>
            <td class="bordered-table">
              @if($val['status']==0)
              unable to open
              @else
               {{$val['status']}}
               @endif
            </td>
        </tr>
        @endforeach

      </tbody>
    </table>
  </div>

</div>

</body>
</html>