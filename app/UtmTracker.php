<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UtmTracker extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'utm_trackers';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'utm_source',
        'utm_medium',
        'utm_campaign',
    ];

    /**
     * Get the user that owns the UTM tracker.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
