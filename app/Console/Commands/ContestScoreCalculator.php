<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Models\Game;
use App\Http\Models\Contest;
use App\Http\Models\Contestwinner;
use App\Http\Models\Contestparticipanthistory;
use Carbon\Carbon;
use Illuminate\Support\Facades\Redis;

class ContestScoreCalculator extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qureka:scorecalculator';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will check contest expiry time and calculate score and store in database & update contest status';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->calculateContestScore();
    }

    public function calculateContestScore()
    {
        //$currenttime = Carbon::now()->toDateTimeString()."<br>\n";//die;
        // echo Carbon::now()->addMinutes(60)->toDateTimeString()."<br>\n";;//die;
        
        //check only 5 minute records which is about to expire
        $contests = Contest::where('end_time', '>=', Carbon::now()->toDateTimeString())
        ->where('end_time', '<=', Carbon::now()->addMinutes(5)->toDateTimeString())
        ->where('status','ACTIVE')
        ->get();

        // p($contests);

        if (!$contests->isEmpty()) 
        { 
            foreach ($contests as $key => $contest) {
                # code...

                 $blockTime = $contest->contest_block_period;
                 $endTime = $contest->end_time;
                $endDate = Carbon::createFromDate($endTime);
                  $now = Carbon::now();
                  $totalsecleft = $endDate->diffInSeconds($now);
                
                //if time left only for block period or over then announce time
                $checkWinnerTime = $totalsecleft - $blockTime;
                //if($checkWinnerTime > 0)
                if($checkWinnerTime <= 0)
                {

                    $matrix = $contest->matrix;
                    $highestnumber = 0;
                    if(!empty($matrix))
                    {
                        foreach ($matrix as $key => $m) {
                            # code...
                            if($highestnumber < $m->to)
                            {
                                $highestnumber = $m->to;
                            }
                        }
                    }
                    $maxRankNode = $highestnumber + 100;

                    $winners = Redis::command('ZREVRANGE',['contest:'.$contest->id,0,$highestnumber,'WITHSCORES']);


                     //p($winners);


                    if(!empty($winners))
                    {
                        $winnersData = array();
                        $participants = array();
                        $position = 1;
                        foreach ($winners as $key => $value) {
                            # code...

                            #check for same rank at same score
                             if($position !== 1){
                                if($preUserScore === $value) {
                                    $position--;
                                }
                            }


                            $earnedPrize = $this->earnedPrize($matrix,$position);

                            $keyData = explode(':', $key);
                            $key = @$keyData[0];
                            if(is_numeric($keyData[0]) && !empty($earnedPrize))
                            {
                                $winnersData[] = array(
                                            'category_id'=>$contest->category_id,
                                            'contest_id'=>$contest->id,
                                            'user_id'=>$key,
                                            'position'=>$position,
                                            'earned_prize'=>$earnedPrize,
                                            'score'=>$value,
                                            'created_at'=>Carbon::now()->toDateTimeString(),
                                            'updated_at'=>Carbon::now()->toDateTimeString()
                                        );
                                $participantsData[] = array(
                                            'contest_id'=>$contest->id,
                                            'user_id'=>$key,
                                            'position'=>$position,
                                            'earned_score'=>$value,
                                            'created_at'=>Carbon::now()->toDateTimeString(),
                                            'updated_at'=>Carbon::now()->toDateTimeString()
                                        );

                            }

                            if($position%100==0)
                            {
                                if(!empty($winnersData) && !empty($participantsData))
                                {
                                    Contestwinner::insert($winnersData);
                                    Contestparticipanthistory::insert($participantsData);
                                }

                                $winnersData = array();
                                $participantsData = array();

                            }
                            
                            // echo $key;
                            // echo $value;
                            $preUserScore = $value;
                            $position++;
                        }

                        //insert in bulk
                        if(!empty($winnersData) && !empty($participantsData))
                        {
                            Contestwinner::insert($winnersData);
                            Contestparticipanthistory::insert($participantsData);
                        }


                        //upon insertion, distribute coins
                        $winners = Contestwinner::where('contest_id',$contest->id)->get();
                        // p($winners);
                        if(!empty($winners))
                        {
                            foreach ($winners as $key => $winner) {
                                # code...
                                $coindistribute = addUserCoin($winner->user_id,$winner->earned_prize,$contest->contest_title);
                            }
                        }
                        

                    }

                    //find contest and mark it expired
                    $contestInfo = Contest::find($contest->id);
                    $contestInfo->status = 'EXPIRED';
                    $contestInfo->save();

                    // p($winners);

                    // echo $highestnumber;die;

                    // p($matrix);
                    //check score stored in redis
                    //remove anonymous score
                    //get max rank and take all records from redis
                    //put batch processing in db
                    //update contest status and exit
                }
            }
        }

        // p($contests);

        // echo Carbon::now()->addMinutes(60)->toDateTimeString();

        // echo Carbon::now()->toDateTimeString(); die;
        // p($contests);
       // echo Carbon::now()->subMinutes(10)->toDateTimeString();
    }
    public function earnedPrize($matrix,$position)
    {
        $amount = 0;
        if(!empty($matrix))
        {
            foreach ($matrix as $key => $m) {
                # code...
                if($m->from <= $position && $m->to >=$position)
                {
                    $amount = $m->amount;
                    break;
                }
            }
        }
        return $amount;
    }
}
