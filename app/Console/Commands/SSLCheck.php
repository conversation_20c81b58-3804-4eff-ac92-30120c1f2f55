<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SSLCheck extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qureka:sslcheck';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will check Expiry of ssl cerification of domains stored in servers_info table daily';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->checkSSLExpiry();
    }
    
    public function checkSSLExpiry(){
        $uptodate = date("Y-m-d", strtotime("+1 week"));
        $data = objToArray(\DB::table('servers_info')->where('status','ACTIVE')->whereDate('ssl_expiry','<=',$uptodate)->select('id','domain_name')->get()->toArray(),'id');
        $result = [];
	//p($data);
        foreach($data as $key=>$val){
            $exp_date = $this->__fetch_ssl_expiry($val['domain_name']);
            if($exp_date){
                //$result[] = array('id'=>$val['id'],'domain_name'=>$val['domain_name'],'domain_expiry'=>$exp_date);
                $result[] = array('id'=>$val['id'],'ssl_expiry'=>$exp_date);
            }
        }
        if($result){
		//echo "result";p($result);
            $udpateRes = $this->__updateBatch('servers_info',$result);
            if($udpateRes){
                echo 'Check SSL Expiry ran';
            }
            else{
                echo 'Check SSL Expiry not updated';
            }
        }
    }

    public function __updateBatch($tableName = "", $multipleData = array()){  
      
        if( $tableName && !empty($multipleData) ) {  
  
            // column or fields to update  
            $updateColumn = array_keys($multipleData[0]);  
            $referenceColumn = $updateColumn[0]; //e.g id  
            unset($updateColumn[0]);  
            $whereIn = "";  
  
            $q = "UPDATE ".$tableName." SET ";   
            foreach ( $updateColumn as $uColumn ) {  
                $q .=  "updated_at= '".date("Y-m-d H:i:s")."' ,".$uColumn." = CASE ";  
  
                foreach( $multipleData as $data ) {  
                    $q .= "WHEN ".$referenceColumn." = ".$data[$referenceColumn]." THEN '".$data[$uColumn]."' ";  
                }  
                $q .= "ELSE ".$uColumn." END, ";  
            }  
            foreach( $multipleData as $data ) {  
                $whereIn .= "'".$data[$referenceColumn]."', ";  
            }  
            $q = rtrim($q, ", ")." WHERE ".$referenceColumn." IN (".  rtrim($whereIn, ', ').")";  
            //p($q);
            // Update    
            return \DB::update(\DB::raw($q));  
  
        } else {  
            return false;  
        }  
  
    }

    public function __fetch_ssl_expiry($url){
        try {
	    //$url = "https://google.com";
            $orignal_parse = parse_url($url, PHP_URL_HOST);
            //$get = stream_context_create(array("ssl" => array("capture_peer_cert" => TRUE)));
	    $get = stream_context_create(array("ssl" => array("capture_peer_cert" => TRUE,'allow_self_signed' => true,
            'verify_peer' => false,
            'verify_peer_name' => false)));
            $read =@stream_socket_client("ssl://".$orignal_parse.":443", $errno, $errstr, 300, STREAM_CLIENT_CONNECT, $get);
            $cert =@stream_context_get_params($read);
            $certinfo =@openssl_x509_parse($cert['options']['ssl']['peer_certificate']);
           //	echo "hi";p($certinfo);
	    if(isset($certinfo['validTo_time_t'])){
                $valid_to = date(DATE_RFC2822,$certinfo['validTo_time_t']);
              //echo "hello".date('Y-m-d',strtotime($valid_to));exit();
		  return date('Y-m-d',strtotime($valid_to));
            }
        }
        catch (exception $e) {
            return "exception";
        }
        
        
    }

}
