<?php

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It's a breeze. Simply tell <PERSON><PERSON> the URIs it should respond to
| and give it the Closure to execute when that URI is requested.
|
*/
 
//die('amgetting loaded');
//array('xssprotection','frameguard','admin','verifyapitoken')
Route::group(['module'=>'cms','namespace' => 'App\modules\api\cms\Controllers','prefix'=>'api/v1','middleware'=>['api']], function() {
	
			// restrcited api by user token
			Route::group(['middleware'=>['verifyusertoken']], function() {

				Route::get('/dashboard', 'CmsController@getDashboard');

				
				
				Route::get('/wallethistory/{id}', 'CmsController@getWalletHistory');
				Route::get('/user/walletbalance', 'CmsController@getUserBalance');
				//Route::get('/contests/{game_id}', 'GamesController@getContests');
				Route::get('/rewardedCoinUser', 'CmsController@rewardedCoinUser');
			});
			//restrcited api by public token
			Route::group(['middleware'=>['verifyapitoken']], function() {

				Route::get('/globalconfig', 'CmsController@getGlobalConfig');
				Route::post('/user', 'CmsController@getUserDetails');
				Route::get('/user/dailybonus', 'CmsController@updateUserBonus');
				
				Route::post('/signup', 'CmsController@getSignup');
				

				//to show intro content + game detail etc
				Route::get('/introcontent', 'CmsController@getIntroContent');


				//get domain status and ads by page
				Route::get('/domaininfo/{domain}', 'CmsController@getDomainInfo');
				//user management - signin signup, forgot password - via social login
				//Route::resource('/users', 'UsersController');
				
				// Route::get('/contests/{game_id}', 'GamesController@getContests');


				#Sample Questions api
				Route::get('/sample-question/{origin}', 'CmsController@getSampleQuestion');
				Route::get('/adstats', 'CmsController@AdsLogReader');

				Route::get('/{category}/contests/{origin}', 'CmsController@getContestList')->where('category', 'quiz|cricket|battle');
				Route::get('/contest/{contest_seo_name}/{origin}', 'CmsController@getContestDetail');

				# Port Coin Api
				Route::get('portcoin/mobile-verify/{mobile_no}', 'CmsController@portCoinMobileVerification');
				Route::get('portcoin/{wallet_id}', 'CmsController@portCoin');

				# Winners Api
				Route::get('/{category}/contests-result/{origin}', 'CmsController@winnersContestList')->where('category', 'quiz|cricket|battle');
				Route::get('/winnerlist/{contest_id}', 'CmsController@getWinnerList');

				# Funquiz Api
				Route::post('/funquiz', 'CmsController@savefunQuizData');
				//Route::get('/funquiz/{src}/{key}', 'CmsController@getfunQuizData');

				Route::post('/tracereader', 'CmsController@tracereader');
				Route::post('/adstracking', 'CmsController@adstracking');
				Route::post('/trackingadtap', 'CmsController@trackingadtap');

				Route::post('/updateuservalidation', 'CmsController@validateUser');
				Route::post('/uservalidationrecordsupdate', 'CmsController@updateUserValidationRecords');
				Route::post('/getunverifieduser', 'CmsController@getNotVerifiedUserRecords');

				Route::get('/fortunecookies', 'CmsController@getFortuneCookies');
				Route::post('/captureadstatus', 'CmsController@captureAdStatus');
			});
	
			Route::get('/funquiz/{src}/{key}', 'CmsController@getfunQuizData');

			Route::get('rc_user/{mobile_no}', 'CmsController@rc_user');
			Route::get('rc_send_otp/{mobile_no}', 'CmsController@rc_send_otp');
			Route::get('rc_update_user/{id}/{mobile_no}', 'CmsController@rc_update_user');
			Route::post('rc_genrate_requestVerifier', 'CmsController@rc_genrate_requestVerifier');
			Route::post('rc_verify_requestVerifier', 'CmsController@rc_verify_requestVerifier');
			Route::post('rc_genrate_authToken', 'CmsController@rc_genrate_authToken');

		
		
});




Route::group(['module'=>'cms','namespace' => 'App\modules\api\cms\Controllers','prefix'=>'api/v2','middleware'=>['api']], function() {
	
			//restrcited api by public token
			Route::group(['middleware'=>['verifyapitoken']], function() {
				
				#Sample Questions api
				Route::get('/sample-question/{origin}', 'CmsVTwoController@getSampleQuestion');
				
				Route::get('/contest/{contest_seo_name}/play', 'CmsVTwoController@getQuestionaireSet');		
				
			});	
});


Route::group(['module'=>'cms','namespace' => 'App\modules\api\cms\Controllers','prefix'=>'api/v3','middleware'=>['api']], function() {
	
			//restrcited api by public token
			Route::group(['middleware'=>['verifyapitoken']], function() {
				
				// #get quiz data
				// Route::get('/contest/{contest_seo_name}/play', 'GamesV3Controller@playContest');	
				// Route::get('/contest/{contest_seo_name}/playnew', 'GamesV3Controller@playContestNew');	

				// #Sample Questions apiv3
				// Route::get('/sample-question/{origin}', 'GamesV3Controller@getSampleQuestion');	
				
			});	
});


Route::group(['module'=>'cms','namespace' => 'App\modules\api\cms\Controllers','prefix'=>'api/v1'], function() {
	Route::post('/qureka_mweb_click_tracker', 'CmsController@qurekaMwebClickTracker');	
});
