<?php 
namespace App\modules\api\cms\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
//use Illuminate\Http\Request;
use App\User;
use App\Http\Models\Accesstoken;
use App\Http\Models\Question;
use Validator;
use Input;
use Redirect;
use DB;
use Exception;
use Carbon\Carbon;
use Lcobucci\JWT\Parser;
use Request;


class CmsVTwoController extends Controller
{

    /**14583
     * Display a listing of the resource.
     *
     * @return Response
     */
    var $lang = 'en';
    public function __construct()
    {
        
    }

    public function getSampleQuestion($origin){

        #Get Data by cache
        $version = rand(0, 100);
        $cacheKey = 'sample_question_v2:'.$version.'_'.$origin;
        $cacheStorage = array();
        $response = checkCacheAvailability($cacheKey);
        if($response)
        {
            return response()->json($response,200);
        }

        $response = array();
        $status = 400;
        $QuestionArray = array();
        $message = '';
        
        $dbName = \Config::get("qureka.qureka_intl_db");
        $db_ext = \DB::connection($dbName);

        $sampleQuestions = $db_ext->table('questionaires')->select('question_title as question','option_1 as option_a','option_2 as option_b','option_3 as option_c','correct_answer as answer')
            ->leftJoin('questionaire_options', function($leftJoin)
            {
                $leftJoin->on('questionaire_options.questionaire_id', '=', 'questionaires.id');
            })
            ->where('questionaires.category_id','1')
            ->where('questionaires.language_code','EN')
             // ->where('questionaires.tags',$origin)
            ->inRandomOrder()->limit(3)
            ->get();
        if(!empty($sampleQuestions))
        {
            $response['question_set'] = $sampleQuestions;
            $status = 200;
            $message = 'Sample Questions';
        }
        
        if($status==200)
        {$cacheStorage = array('key'=>$cacheKey,'ttl'=>900);}
        return  $this->response($response,$status,$message,array('cache'=>$cacheStorage));
                
    }

    public function getQuestionaireSet($seo_name)
    {
         # Question set by SEO Name
        $seo_name = base64_decode($seo_name);
    
        $version = rand(0, 50);
        $cacheKey = 'contest_v2:'.$version.$seo_name;
        $cacheStorage = array();
        $response = checkCacheAvailability($cacheKey);
        if($response)
        {
            die('cache');
            return response()->json($response,200);
        }

        $response = array();
        $status = 400;
        $contestArray = array();
        $message = '';
        
        if(!empty($seo_name))
        { 
            
            $dbName = \Config::get("qureka.qureka_intl_db");
            $db_ext = \DB::connection($dbName);

            $questionset = $db_ext->table('questionaires')->select('questionaires.id as qid','question_title as question','option_1 as option_a','option_2 as option_b','option_3 as option_c','correct_answer as answer')
                     ->leftJoin('questionaire_options', function($leftJoin)
                    {
                        $leftJoin->on('questionaire_options.questionaire_id', '=', 'questionaires.id');
                    })->where('questionaires.category_id', $seo_name)
                     ->where('questionaires.language_code','EN')
                     ->where('questionaires.status','ACTIVE')
                     ->inRandomOrder()->limit(3)
                    ->get();
            
            if(!empty($questionset))
            {
                $response['question_set'] = $questionset;
                $status = 200;
                $message = 'Questions Set';
            }
        }
        if($status==200)
        {$cacheStorage = array('key'=>$cacheKey,'ttl'=>900);}
        return  $this->response($response,$status,$message,array('cache'=>$cacheStorage));
    
    }
}
        
