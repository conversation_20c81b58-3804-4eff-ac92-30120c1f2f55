<?php namespace App\modules\backend\cms\Controllers;

		    use App\Http\Requests;
			use App\Http\Controllers\Controller;
			use App\Http\Models\User;
			use Validator;
			use Redirect;
			use Request;

			class UsersController extends Controller {

			/**
			 * Display a listing of the resource.
			 *
			 * @return Response
			 */
			public function __construct()
			{
				$this->middleware('adminauth');
			}

			public function index()
			{ 
				if(is_axios()==true)
				{
					//filter params 
					$inputs = Request::all();
					$inputs = array_filter($inputs);
				$email = @$inputs['email'];
				$password = @$inputs['password'];
				$users = User::where(array())
				->when($email, function ($query) use ($email) {
				        	return $query->where('email','LIKE' ,$email.'%');})
				->when($password, function ($query) use ($password) {
				        	return $query->where('password','LIKE' ,$password.'%');})
				->paginate(20);
			      $users->setPath('users');
					$users->appends(Request::except('page'));
					return view('cms::users.index_view')->with('users', $users);

				}
				return view('cms::users.index');
			}

			/**
			 * Show the form for creating a new resource.
			 *
			 * @return Response
			 */
			public function create()
			{
				//
				return view('cms::users.create');
			}

			/**
			 * Store a newly created resource in storage.
			 *
			 * @return Response
			 */
			public function store()
			{
				//
				// validate
		        // read more on validation at http://laravel.com/docs/validation
		        	$status = 200;
			        $response = array();
			        $message = '';

		        
				$rules = array('email'=>'required',
			        'password'=>'required',
			        );

				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        
		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		        	$status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		            
		        } else {
		            // store
		            $users = new User;
		            $users->email = $inputs['email'];
				        		$users->password = $inputs['password'];
				        		$users->save();

		           //trigger seo url
					triggerSeoUrls($users->id,'user',$inputs['main_seo_title']);
						$message = 'Successfully created Users!';

		            
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Display the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function show($id)
			{
				//
				if($id=='quickupdate')
				{
					return $this->quickUpdate();
				}
				
				 $users = User::find($id);

				 if(empty($users))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;
				 	
				 }

		        // show the view and pass the users to it
		        return view('cms::users.show')->with('users', $users);
			}


			//to update status quickly

			public function quickUpdate()
			{
				$inputs = Request::all();
				if(isset($inputs['id']) && isset($inputs['action']))
				{
					if($inputs['action']==1){$action='ACTIVE';}
					else if($inputs['action']==2){$action='INACTIVE';}
					else if($inputs['action']==3){$action='DELETED';}
					if($action!='')
					{User::whereIn('id',explode(',', $inputs['id']))->update(['status'=>$action]);}
				}
			}

			/**
			 * Show the form for editing the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function edit($id)
			{
				//
				$users = User::find($id);
				if(empty($users))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;

				 }

		        // show the view and pass the users to it
		        return view('cms::users.edit')->with('users', $users);
			}

			/**
			 * Update the specified resource in storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function update($id)
			{
				// read more on validation at http://laravel.com/docs/validation
		       		$status = 200;
			        $response = array();
			        $message = '';
		       $rules = array();
				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		            $status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		        } else {
		            // store
		             $users = User::find($id);$users->email = $inputs['email'];
				        		$users->password = $inputs['password'];
				        		$users->save();

		           //trigger seo url
					triggerSeoUrls($users->id,'user',$inputs['main_seo_title']);
						$message = 'Successfully updated Users!';
		           
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Remove the specified resource from storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function destroy($id)
			{
				//
					$status = 200;
			        $response = array();
			        $message = '';
				$users = User::find($id);
				if(empty($users))
				 { 
				 	$status=421; 
				 	$response['errors']=array('No Users found!');
				 	$message='No Users found!';
				 }
				 else
				 {
				 	 $users->delete();
				 	 $message = 'Successfully deleted Users!';

				 }
				 return $this->response($response,$status,$message);
			 
			}

		}
		