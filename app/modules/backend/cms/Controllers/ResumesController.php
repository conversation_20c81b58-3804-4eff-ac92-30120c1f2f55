<?php namespace App\modules\backend\cms\Controllers;

		    use App\Http\Requests;
			use App\Http\Controllers\Controller;
			use App\Http\Models\Admin;
			use Validator;
			use Redirect;
			use Request;

			class ResumesController extends Controller {

			/**
			 * Display a listing of the resource.
			 *
			 * @return Response
			 */
			public function __construct()
			{
				$this->middleware('adminauth');
			}

			public function users()
			{ 
				if(is_axios()==true)
				{
					//filter params 
					$inputs = Request::all();
					$inputs = array_filter($inputs);
					// p($inputs);
					$name 				= @$inputs['name'];
					$email 				= @$inputs['email'];
					$source 			= @$inputs['source'];
					$utm_source 		= @$inputs['utm_source'];
					$utm_medium 		= @$inputs['utm_medium'];
					$utm_campaign 		= @$inputs['utm_campaign'];
					$reg_start_time 	= @$inputs['reg_start_time'];
					$reg_end_time 		= @$inputs['reg_end_time'];
					$login_start_time 	= @$inputs['login_start_time'];
					$login_end_time 	= @$inputs['login_end_time'];

					$resumeusers = \DB::connection('resume_mysql')->table('User')
					->select('User.*','UTMTracking.utm_source','UTMTracking.utm_medium','UTMTracking.utm_campaign')
					->leftjoin('UTMTracking','User.id','UTMTracking.user_id')
					->where(array())
					->when($name, function ($query) use ($name) {return $query->where('name','LIKE' ,'%'.$name.'%');})
					->when($email, function ($query) use ($email) {return $query->where('email','LIKE' ,'%'.$email.'%');})
					->when($source, function ($query) use ($source) {return $query->where('source','=' ,$source);})
					->when($utm_source, function ($query) use ($utm_source) {return $query->where('utm_source','LIKE' ,'%'.$utm_source.'%');})
					->when($utm_medium, function ($query) use ($utm_medium) {return $query->where('utm_medium','LIKE' ,'%'.$utm_medium.'%');})
					->when($utm_campaign, function ($query) use ($utm_campaign) {return $query->where('utm_campaign','LIKE' ,'%'.$utm_campaign.'%');})
					->when($reg_start_time, function ($query) use ($reg_start_time,$reg_end_time) {return $query->whereBetween('createdAt' ,[$reg_start_time,$reg_end_time]);})
					->when($login_start_time, function ($query) use ($login_start_time,$login_end_time) {return $query->whereBetween('updatedAt' ,[$login_start_time,$login_end_time]);})
					->paginate(50);
					// p($resumeusers->toArray());

					$resumeusers->setPath('resumeusers');
					$resumeusers->appends(Request::except('page'));
					return view('cms::resumeUsers.index_view')->with('resumeusers', $resumeusers);
				}
				return view('cms::resumeUsers.index');
			}

			public function resumes()
			{ 
				if(is_axios()==true)
				{
					//filter params 
					$inputs = Request::all();
					$inputs = array_filter($inputs);
					// p($inputs);
					$name 		= @$inputs['Name'];
					$email 		= @$inputs['Email'];
					$title 		= @$inputs['title'];
					$template 	= @$inputs['template'];
					$GitHub 	= @$inputs['GitHub'];
					$LinkedIn 	= @$inputs['LinkedIn'];
					$Phone 		= @$inputs['Phone'];
					$Location 	= @$inputs['Location'];
					$JobTitle 	= @$inputs['JobTitle'];
					$LinkedIn 	= @$inputs['LinkedIn'];

					
					$created_start_time 	= @$inputs['created_start_time'];
					$created_end_time 		= @$inputs['created_end_time'];
					$updated_start_time 	= @$inputs['updated_start_time'];
					$updated_end_time 	= @$inputs['updated_end_time'];

					$resumelists = \DB::connection('resume_mysql')->table('Resume')
					->where(array())
					->when($name, function ($query) use ($name) {return $query->where('Name','LIKE' ,'%'.$name.'%');})
					->when($email, function ($query) use ($email) {return $query->where('Email','LIKE' ,'%'.$email.'%');})
					->when($title, function ($query) use ($title) {return $query->where('title','=' ,$title);})
					->when($template, function ($query) use ($template) {return $query->where('template','LIKE' ,'%'.$template.'%');})
					->when($GitHub, function ($query) use ($GitHub) {return $query->where('GitHub','LIKE' ,'%'.$GitHub.'%');})
					->when($Phone, function ($query) use ($Phone) {return $query->where('Phone','LIKE' ,'%'.$Phone.'%');})
					->when($Location, function ($query) use ($Location) {return $query->where('Location','LIKE' ,'%'.$Location.'%');})
					->when($JobTitle, function ($query) use ($JobTitle) {return $query->where('JobTitle','LIKE' ,'%'.$JobTitle.'%');})
					->when($LinkedIn, function ($query) use ($LinkedIn) {return $query->where('LinkedIn','LIKE' ,'%'.$LinkedIn.'%');})
					
					->when($created_start_time, function ($query) use ($created_start_time,$created_end_time) {return $query->whereBetween('createdAt' ,[$created_start_time,$created_end_time]);})
					->when($updated_start_time, function ($query) use ($updated_start_time,$updated_end_time) {return $query->whereBetween('updatedAt' ,[$updated_start_time,$updated_end_time]);})
					->paginate(20);
					// ->toSql();
					// p($resumelists);
					// p($resumelists->toArray());

					$resumelists->setPath('resumelists');
					$resumelists->appends(Request::except('page'));
					return view('cms::resumelists.index_view')->with('resumelists', $resumelists);
				}
				return view('cms::resumelists.index');
			}
		}
		