<?php namespace App\modules\backend\cms\Controllers;

		    use App\Http\Requests;
			use App\Http\Controllers\Controller;
			use App\Http\Models\Jobroleprompt;
			use Validator;
			use Redirect;
			use Request;

			class JobrolepromptsController extends Controller {

			/**
			 * Display a listing of the resource.
			 *
			 * @return Response
			 */
			public function __construct()
			{
				$this->middleware('adminauth');
			}

			public function index()
			{ 
				if(is_axios()==true)
				{
					//filter params 
					$inputs = Request::all();
					$inputs = array_filter($inputs);
				$jobrole_id = @$inputs['jobrole_id'];
				$exp_min_in_year = @$inputs['exp_min_in_year'];
				$exp_max_in_year = @$inputs['exp_max_in_year'];
				$jobrole_prompts = Jobroleprompt::where(array())
				->when($jobrole_id, function ($query) use ($jobrole_id) {
				        	return $query->where('jobrole_id','=' ,$jobrole_id);})
				->when($exp_min_in_year, function ($query) use ($exp_min_in_year) {
				        	return $query->where('exp_min_in_year','=' ,$exp_min_in_year);})
				->when($exp_max_in_year, function ($query) use ($exp_max_in_year) {
				        	return $query->where('exp_max_in_year','=' ,$exp_max_in_year);})
				->paginate(20);
			      $jobrole_prompts->setPath('jobroleprompts');
					$jobrole_prompts->appends(Request::except('page'));
					return view('cms::jobroleprompts.index_view')->with('jobrole_prompts', $jobrole_prompts);

				}
				return view('cms::jobroleprompts.index');
			}

			/**
			 * Show the form for creating a new resource.
			 *
			 * @return Response
			 */
			public function create()
			{
				//
				return view('cms::jobroleprompts.create');
			}

			/**
			 * Store a newly created resource in storage.
			 *
			 * @return Response
			 */
			public function store()
			{
				//
				// validate
		        // read more on validation at http://laravel.com/docs/validation
		        	$status = 200;
			        $response = array();
			        $message = '';

		        
				$rules = array('jobrole_id'=>'required',
			        'exp_min_in_year'=>'required',
			        'exp_max_in_year'=>'required',
			        );

				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        
		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		        	$status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		            
		        } else {
		            // store
		            $jobroleprompts = new Jobroleprompt;
		            $jobroleprompts->jobrole_id = $inputs['jobrole_id'];
				        		$jobroleprompts->exp_min_in_year = $inputs['exp_min_in_year'];
				        		$jobroleprompts->exp_max_in_year = $inputs['exp_max_in_year'];
				        		$jobroleprompts->prompt1 = $inputs['prompt1'];
				        		$jobroleprompts->prompt2 = $inputs['prompt2'];
				        		$jobroleprompts->prompt3 = $inputs['prompt3'];
				        		$jobroleprompts->save();

		           //trigger seo url
					triggerSeoUrls($jobroleprompts->id,'jobroleprompt',$inputs['main_seo_title']);
						$message = 'Successfully created Jobroleprompts!';

		            
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Display the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function show($id)
			{
				//
				if($id=='quickupdate')
				{
					return $this->quickUpdate();
				}
				
				 $jobroleprompts = Jobroleprompt::find($id);

				 if(empty($jobroleprompts))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;
				 	
				 }

		        // show the view and pass the jobroleprompts to it
		        return view('cms::jobroleprompts.show')->with('jobroleprompts', $jobroleprompts);
			}


			//to update status quickly

			public function quickUpdate()
			{
				$inputs = Request::all();
				if(isset($inputs['id']) && isset($inputs['action']))
				{
					if($inputs['action']==1){$action='ACTIVE';}
					else if($inputs['action']==2){$action='INACTIVE';}
					else if($inputs['action']==3){$action='DELETED';}
					if($action!='')
					{Jobroleprompt::whereIn('id',explode(',', $inputs['id']))->update(['status'=>$action]);}
				}
			}

			/**
			 * Show the form for editing the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function edit($id)
			{
				//
				$jobroleprompts = Jobroleprompt::find($id);
				if(empty($jobroleprompts))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;

				 }

		        // show the view and pass the jobroleprompts to it
		        return view('cms::jobroleprompts.edit')->with('jobroleprompts', $jobroleprompts);
			}

			/**
			 * Update the specified resource in storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function update($id)
			{
				// read more on validation at http://laravel.com/docs/validation
		       		$status = 200;
			        $response = array();
			        $message = '';
		       $rules = array();
				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		            $status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		        } else {
		            // store
		             $jobroleprompts = Jobroleprompt::find($id);$jobroleprompts->jobrole_id = $inputs['jobrole_id'];
				        		$jobroleprompts->exp_min_in_year = $inputs['exp_min_in_year'];
				        		$jobroleprompts->exp_max_in_year = $inputs['exp_max_in_year'];
				        		$jobroleprompts->prompt1 = $inputs['prompt1'];
				        		$jobroleprompts->prompt2 = $inputs['prompt2'];
				        		$jobroleprompts->prompt3 = $inputs['prompt3'];
				        		$jobroleprompts->save();

		           //trigger seo url
					triggerSeoUrls($jobroleprompts->id,'jobroleprompt',$inputs['main_seo_title']);
						$message = 'Successfully updated Jobroleprompts!';
		           
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Remove the specified resource from storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function destroy($id)
			{
				//
					$status = 200;
			        $response = array();
			        $message = '';
				$jobroleprompts = Jobroleprompt::find($id);
				if(empty($jobroleprompts))
				 { 
				 	$status=421; 
				 	$response['errors']=array('No Jobroleprompts found!');
				 	$message='No Jobroleprompts found!';
				 }
				 else
				 {
				 	 $jobroleprompts->delete();
				 	 $message = 'Successfully deleted Jobroleprompts!';

				 }
				 return $this->response($response,$status,$message);
			 
			}

		}
		