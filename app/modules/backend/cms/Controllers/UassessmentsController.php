<?php namespace App\modules\backend\cms\Controllers;

		    use App\Http\Requests;
			use App\Http\Controllers\Controller;
			use App\Http\Models\Uassessment;
			use Validator;
			use Redirect;
			use Request;

			class UassessmentsController extends Controller {

			/**
			 * Display a listing of the resource.
			 *
			 * @return Response
			 */
			public function __construct()
			{
				$this->middleware('adminauth');
			}

			public function index()
			{ 
				if(is_axios()==true)
				{
					//filter params 
					$inputs = Request::all();
					$inputs = array_filter($inputs);
				$user_profile_id = @$inputs['user_profile_id'];
				$user_assessments = Uassessment::where(array())
				->when($user_profile_id, function ($query) use ($user_profile_id) {
				        	return $query->where('user_profile_id','=' ,$user_profile_id);})
				->paginate(20);
			      $user_assessments->setPath('uassessments');
					$user_assessments->appends(Request::except('page'));
					return view('cms::uassessments.index_view')->with('user_assessments', $user_assessments);

				}
				return view('cms::uassessments.index');
			}

			/**
			 * Show the form for creating a new resource.
			 *
			 * @return Response
			 */
			public function create()
			{
				//
				return view('cms::uassessments.create');
			}

			/**
			 * Store a newly created resource in storage.
			 *
			 * @return Response
			 */
			public function store()
			{
				//
				// validate
		        // read more on validation at http://laravel.com/docs/validation
		        	$status = 200;
			        $response = array();
			        $message = '';

		        
				$rules = array('user_profile_id'=>'required',
			        );

				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        
		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		        	$status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		            
		        } else {
		            // store
		            $uassessments = new Uassessment;
		            $uassessments->user_profile_id = $inputs['user_profile_id'];
				        		$uassessments->save();

		           //trigger seo url
					triggerSeoUrls($uassessments->id,'uassessment',$inputs['main_seo_title']);
						$message = 'Successfully created Uassessments!';

		            
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Display the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function show($id)
			{
				//
				if($id=='quickupdate')
				{
					return $this->quickUpdate();
				}
				
				 $uassessments = Uassessment::find($id);

				 if(empty($uassessments))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;
				 	
				 }

		        // show the view and pass the uassessments to it
		        return view('cms::uassessments.show')->with('uassessments', $uassessments);
			}


			//to update status quickly

			public function quickUpdate()
			{
				$inputs = Request::all();
				if(isset($inputs['id']) && isset($inputs['action']))
				{
					if($inputs['action']==1){$action='ACTIVE';}
					else if($inputs['action']==2){$action='INACTIVE';}
					else if($inputs['action']==3){$action='DELETED';}
					if($action!='')
					{Uassessment::whereIn('id',explode(',', $inputs['id']))->update(['status'=>$action]);}
				}
			}

			/**
			 * Show the form for editing the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function edit($id)
			{
				//
				$uassessments = Uassessment::find($id);
				if(empty($uassessments))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;

				 }

		        // show the view and pass the uassessments to it
		        return view('cms::uassessments.edit')->with('uassessments', $uassessments);
			}

			/**
			 * Update the specified resource in storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function update($id)
			{
				// read more on validation at http://laravel.com/docs/validation
		       		$status = 200;
			        $response = array();
			        $message = '';
		       $rules = array();
				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		            $status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		        } else {
		            // store
		             $uassessments = Uassessment::find($id);$uassessments->user_profile_id = $inputs['user_profile_id'];
				        		$uassessments->save();

		           //trigger seo url
					triggerSeoUrls($uassessments->id,'uassessment',$inputs['main_seo_title']);
						$message = 'Successfully updated Uassessments!';
		           
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Remove the specified resource from storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function destroy($id)
			{
				//
					$status = 200;
			        $response = array();
			        $message = '';
				$uassessments = Uassessment::find($id);
				if(empty($uassessments))
				 { 
				 	$status=421; 
				 	$response['errors']=array('No Uassessments found!');
				 	$message='No Uassessments found!';
				 }
				 else
				 {
				 	 $uassessments->delete();
				 	 $message = 'Successfully deleted Uassessments!';

				 }
				 return $this->response($response,$status,$message);
			 
			}

		}
		