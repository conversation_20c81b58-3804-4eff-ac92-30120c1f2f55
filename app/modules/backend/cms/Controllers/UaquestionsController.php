<?php namespace App\modules\backend\cms\Controllers;

		    use App\Http\Requests;
			use App\Http\Controllers\Controller;
			use App\Http\Models\Uaquestion;
			use Validator;
			use Redirect;
			use Request;

			class UaquestionsController extends Controller {

			/**
			 * Display a listing of the resource.
			 *
			 * @return Response
			 */
			public function __construct()
			{
				$this->middleware('adminauth');
			}

			public function index()
			{ 
				if(is_axios()==true)
				{
					//filter params 
					$inputs = Request::all();
					$inputs = array_filter($inputs);
				$user_assessment_id = @$inputs['user_assessment_id'];
				$question = @$inputs['question'];
				$level = @$inputs['level'];
				$token = @$inputs['token'];
				$user_assessment_questions = Uaquestion::where(array())
				->when($user_assessment_id, function ($query) use ($user_assessment_id) {
				        	return $query->where('user_assessment_id','=' ,$user_assessment_id);})
				->when($question, function ($query) use ($question) {
				        	return $query->where('question','LIKE' ,$question.'%');})
				->when($level, function ($query) use ($level) {
				        	return $query->where('level','LIKE' ,$level.'%');})
				->when($token, function ($query) use ($token) {
				        	return $query->where('token','=' ,$token);})
				->paginate(20);
			      $user_assessment_questions->setPath('uaquestions');
					$user_assessment_questions->appends(Request::except('page'));
					return view('cms::uaquestions.index_view')->with('user_assessment_questions', $user_assessment_questions);

				}
				return view('cms::uaquestions.index');
			}

			/**
			 * Show the form for creating a new resource.
			 *
			 * @return Response
			 */
			public function create()
			{
				//
				return view('cms::uaquestions.create');
			}

			/**
			 * Store a newly created resource in storage.
			 *
			 * @return Response
			 */
			public function store()
			{
				//
				// validate
		        // read more on validation at http://laravel.com/docs/validation
		        	$status = 200;
			        $response = array();
			        $message = '';

		        
				$rules = array('user_assessment_id'=>'required',
			        'question'=>'required',
			        'level'=>'required',
			        'token'=>'required',
			        );

				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        
		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		        	$status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		            
		        } else {
		            // store
		            $uaquestions = new Uaquestion;
		            $uaquestions->user_assessment_id = $inputs['user_assessment_id'];
				        		$uaquestions->question = $inputs['question'];
				        		$uaquestions->answer = $inputs['answer'];
				        		$uaquestions->level = $inputs['level'];
				        		$uaquestions->token = $inputs['token'];
				        		$uaquestions->save();

		           //trigger seo url
					triggerSeoUrls($uaquestions->id,'uaquestion',$inputs['main_seo_title']);
						$message = 'Successfully created Uaquestions!';

		            
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Display the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function show($id)
			{
				//
				if($id=='quickupdate')
				{
					return $this->quickUpdate();
				}
				
				 $uaquestions = Uaquestion::find($id);

				 if(empty($uaquestions))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;
				 	
				 }

		        // show the view and pass the uaquestions to it
		        return view('cms::uaquestions.show')->with('uaquestions', $uaquestions);
			}


			//to update status quickly

			public function quickUpdate()
			{
				$inputs = Request::all();
				if(isset($inputs['id']) && isset($inputs['action']))
				{
					if($inputs['action']==1){$action='ACTIVE';}
					else if($inputs['action']==2){$action='INACTIVE';}
					else if($inputs['action']==3){$action='DELETED';}
					if($action!='')
					{Uaquestion::whereIn('id',explode(',', $inputs['id']))->update(['status'=>$action]);}
				}
			}

			/**
			 * Show the form for editing the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function edit($id)
			{
				//
				$uaquestions = Uaquestion::find($id);
				if(empty($uaquestions))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;

				 }

		        // show the view and pass the uaquestions to it
		        return view('cms::uaquestions.edit')->with('uaquestions', $uaquestions);
			}

			/**
			 * Update the specified resource in storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function update($id)
			{
				// read more on validation at http://laravel.com/docs/validation
		       		$status = 200;
			        $response = array();
			        $message = '';
		       $rules = array();
				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		            $status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		        } else {
		            // store
		             $uaquestions = Uaquestion::find($id);$uaquestions->user_assessment_id = $inputs['user_assessment_id'];
				        		$uaquestions->question = $inputs['question'];
				        		$uaquestions->answer = $inputs['answer'];
				        		$uaquestions->level = $inputs['level'];
				        		$uaquestions->token = $inputs['token'];
				        		$uaquestions->save();

		           //trigger seo url
					triggerSeoUrls($uaquestions->id,'uaquestion',$inputs['main_seo_title']);
						$message = 'Successfully updated Uaquestions!';
		           
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Remove the specified resource from storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function destroy($id)
			{
				//
					$status = 200;
			        $response = array();
			        $message = '';
				$uaquestions = Uaquestion::find($id);
				if(empty($uaquestions))
				 { 
				 	$status=421; 
				 	$response['errors']=array('No Uaquestions found!');
				 	$message='No Uaquestions found!';
				 }
				 else
				 {
				 	 $uaquestions->delete();
				 	 $message = 'Successfully deleted Uaquestions!';

				 }
				 return $this->response($response,$status,$message);
			 
			}

		}
		