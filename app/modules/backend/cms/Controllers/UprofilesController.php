<?php namespace App\modules\backend\cms\Controllers;

		    use App\Http\Requests;
			use App\Http\Controllers\Controller;
			use App\Http\Models\Uprofile;
			use Validator;
			use Redirect;
			use Request;

			class UprofilesController extends Controller {

			/**
			 * Display a listing of the resource.
			 *
			 * @return Response
			 */
			public function __construct()
			{
				$this->middleware('adminauth');
			}

			public function index()
			{ 
				if(is_axios()==true)
				{
					//filter params 
					$inputs = Request::all();
					$inputs = array_filter($inputs);
				$user_id = @$inputs['user_id'];
				$job_role_id = @$inputs['job_role_id'];
				$exp_in_year = @$inputs['exp_in_year'];
				$is_ai_used = @$inputs['is_ai_used'];
				$mobile_number = @$inputs['mobile_number'];
				$current_company = @$inputs['current_company'];
				$current_role = @$inputs['current_role'];
				$user_profiles = Uprofile::where(array())
				->when($user_id, function ($query) use ($user_id) {
				        	return $query->where('user_id','=' ,$user_id);})
				->when($job_role_id, function ($query) use ($job_role_id) {
				        	return $query->where('job_role_id','=' ,$job_role_id);})
				->when($exp_in_year, function ($query) use ($exp_in_year) {
				        	return $query->where('exp_in_year','=' ,$exp_in_year);})
				->when($is_ai_used, function ($query) use ($is_ai_used) {
				        	return $query->where('is_ai_used','LIKE' ,$is_ai_used.'%');})
				->when($mobile_number, function ($query) use ($mobile_number) {
				        	return $query->where('mobile_number','LIKE' ,$mobile_number.'%');})
				->when($current_company, function ($query) use ($current_company) {
				        	return $query->where('current_company','LIKE' ,$current_company.'%');})
				->when($current_role, function ($query) use ($current_role) {
				        	return $query->where('current_role','LIKE' ,$current_role.'%');})
				->paginate(20);
			      $user_profiles->setPath('uprofiles');
					$user_profiles->appends(Request::except('page'));
					return view('cms::uprofiles.index_view')->with('user_profiles', $user_profiles);

				}
				return view('cms::uprofiles.index');
			}

			/**
			 * Show the form for creating a new resource.
			 *
			 * @return Response
			 */
			public function create()
			{
				//
				return view('cms::uprofiles.create');
			}

			/**
			 * Store a newly created resource in storage.
			 *
			 * @return Response
			 */
			public function store()
			{
				//
				// validate
		        // read more on validation at http://laravel.com/docs/validation
		        	$status = 200;
			        $response = array();
			        $message = '';

		        
				$rules = array('user_id'=>'required',
			        'job_role_id'=>'required',
			        'exp_in_year'=>'required',
			        'is_ai_used'=>'required',
			        'mobile_number'=>'required',
			        'current_company'=>'required',
			        'current_role'=>'required',
			        );

				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        
		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		        	$status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		            
		        } else {
		            // store
		            $uprofiles = new Uprofile;
		            $uprofiles->user_id = $inputs['user_id'];
				        		$uprofiles->job_role_id = $inputs['job_role_id'];
				        		$uprofiles->exp_in_year = $inputs['exp_in_year'];
				        		$uprofiles->is_ai_used = $inputs['is_ai_used'];
				        		$uprofiles->mobile_number = $inputs['mobile_number'];
				        		$uprofiles->current_company = $inputs['current_company'];
				        		$uprofiles->current_role = $inputs['current_role'];
				        		$uprofiles->save();

		           //trigger seo url
					triggerSeoUrls($uprofiles->id,'uprofile',$inputs['main_seo_title']);
						$message = 'Successfully created Uprofiles!';

		            
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Display the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function show($id)
			{
				//
				if($id=='quickupdate')
				{
					return $this->quickUpdate();
				}
				
				 $uprofiles = Uprofile::find($id);

				 if(empty($uprofiles))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;
				 	
				 }

		        // show the view and pass the uprofiles to it
		        return view('cms::uprofiles.show')->with('uprofiles', $uprofiles);
			}


			//to update status quickly

			public function quickUpdate()
			{
				$inputs = Request::all();
				if(isset($inputs['id']) && isset($inputs['action']))
				{
					if($inputs['action']==1){$action='ACTIVE';}
					else if($inputs['action']==2){$action='INACTIVE';}
					else if($inputs['action']==3){$action='DELETED';}
					if($action!='')
					{Uprofile::whereIn('id',explode(',', $inputs['id']))->update(['status'=>$action]);}
				}
			}

			/**
			 * Show the form for editing the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function edit($id)
			{
				//
				$uprofiles = Uprofile::find($id);
				if(empty($uprofiles))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;

				 }

		        // show the view and pass the uprofiles to it
		        return view('cms::uprofiles.edit')->with('uprofiles', $uprofiles);
			}

			/**
			 * Update the specified resource in storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function update($id)
			{
				// read more on validation at http://laravel.com/docs/validation
		       		$status = 200;
			        $response = array();
			        $message = '';
		       $rules = array();
				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		            $status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		        } else {
		            // store
		             $uprofiles = Uprofile::find($id);$uprofiles->user_id = $inputs['user_id'];
				        		$uprofiles->job_role_id = $inputs['job_role_id'];
				        		$uprofiles->exp_in_year = $inputs['exp_in_year'];
				        		$uprofiles->is_ai_used = $inputs['is_ai_used'];
				        		$uprofiles->mobile_number = $inputs['mobile_number'];
				        		$uprofiles->current_company = $inputs['current_company'];
				        		$uprofiles->current_role = $inputs['current_role'];
				        		$uprofiles->save();

		           //trigger seo url
					triggerSeoUrls($uprofiles->id,'uprofile',$inputs['main_seo_title']);
						$message = 'Successfully updated Uprofiles!';
		           
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Remove the specified resource from storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function destroy($id)
			{
				//
					$status = 200;
			        $response = array();
			        $message = '';
				$uprofiles = Uprofile::find($id);
				if(empty($uprofiles))
				 { 
				 	$status=421; 
				 	$response['errors']=array('No Uprofiles found!');
				 	$message='No Uprofiles found!';
				 }
				 else
				 {
				 	 $uprofiles->delete();
				 	 $message = 'Successfully deleted Uprofiles!';

				 }
				 return $this->response($response,$status,$message);
			 
			}

		}
		