<?php namespace App\modules\backend\cms\Controllers;

		    use App\Http\Requests;
			use App\Http\Controllers\Controller;
			use App\Http\Models\Uareport;
			use Validator;
			use Redirect;
			use Request;

			class UareportsController extends Controller {

			/**
			 * Display a listing of the resource.
			 *
			 * @return Response
			 */
			public function __construct()
			{
				$this->middleware('adminauth');
			}

			public function index()
			{ 
				if(is_axios()==true)
				{
					//filter params 
					$inputs = Request::all();
					$inputs = array_filter($inputs);
				$user_assessment_id = @$inputs['user_assessment_id'];
				$answer = @$inputs['answer'];
				$user_assessment_reports = Uareport::where(array())
				->when($user_assessment_id, function ($query) use ($user_assessment_id) {
				        	return $query->where('user_assessment_id','=' ,$user_assessment_id);})
				->when($answer, function ($query) use ($answer) {
				        	return $query->where('answer','LIKE' ,$answer.'%');})
				->paginate(20);
			      $user_assessment_reports->setPath('uareports');
					$user_assessment_reports->appends(Request::except('page'));
					return view('cms::uareports.index_view')->with('user_assessment_reports', $user_assessment_reports);

				}
				return view('cms::uareports.index');
			}

			/**
			 * Show the form for creating a new resource.
			 *
			 * @return Response
			 */
			public function create()
			{
				//
				return view('cms::uareports.create');
			}

			/**
			 * Store a newly created resource in storage.
			 *
			 * @return Response
			 */
			public function store()
			{
				//
				// validate
		        // read more on validation at http://laravel.com/docs/validation
		        	$status = 200;
			        $response = array();
			        $message = '';

		        
				$rules = array('user_assessment_id'=>'required',
			        'answer'=>'required',
			        );

				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        
		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		        	$status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		            
		        } else {
		            // store
		            $uareports = new Uareport;
		            $uareports->user_assessment_id = $inputs['user_assessment_id'];
				        		$uareports->answer = $inputs['answer'];
				        		$uareports->save();

		           //trigger seo url
					triggerSeoUrls($uareports->id,'uareport',$inputs['main_seo_title']);
						$message = 'Successfully created Uareports!';

		            
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Display the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function show($id)
			{
				//
				if($id=='quickupdate')
				{
					return $this->quickUpdate();
				}
				
				 $uareports = Uareport::find($id);

				 if(empty($uareports))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;
				 	
				 }

		        // show the view and pass the uareports to it
		        return view('cms::uareports.show')->with('uareports', $uareports);
			}


			//to update status quickly

			public function quickUpdate()
			{
				$inputs = Request::all();
				if(isset($inputs['id']) && isset($inputs['action']))
				{
					if($inputs['action']==1){$action='ACTIVE';}
					else if($inputs['action']==2){$action='INACTIVE';}
					else if($inputs['action']==3){$action='DELETED';}
					if($action!='')
					{Uareport::whereIn('id',explode(',', $inputs['id']))->update(['status'=>$action]);}
				}
			}

			/**
			 * Show the form for editing the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function edit($id)
			{
				//
				$uareports = Uareport::find($id);
				if(empty($uareports))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;

				 }

		        // show the view and pass the uareports to it
		        return view('cms::uareports.edit')->with('uareports', $uareports);
			}

			/**
			 * Update the specified resource in storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function update($id)
			{
				// read more on validation at http://laravel.com/docs/validation
		       		$status = 200;
			        $response = array();
			        $message = '';
		       $rules = array();
				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		            $status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		        } else {
		            // store
		             $uareports = Uareport::find($id);$uareports->user_assessment_id = $inputs['user_assessment_id'];
				        		$uareports->answer = $inputs['answer'];
				        		$uareports->save();

		           //trigger seo url
					triggerSeoUrls($uareports->id,'uareport',$inputs['main_seo_title']);
						$message = 'Successfully updated Uareports!';
		           
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Remove the specified resource from storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function destroy($id)
			{
				//
					$status = 200;
			        $response = array();
			        $message = '';
				$uareports = Uareport::find($id);
				if(empty($uareports))
				 { 
				 	$status=421; 
				 	$response['errors']=array('No Uareports found!');
				 	$message='No Uareports found!';
				 }
				 else
				 {
				 	 $uareports->delete();
				 	 $message = 'Successfully deleted Uareports!';

				 }
				 return $this->response($response,$status,$message);
			 
			}

		}
		