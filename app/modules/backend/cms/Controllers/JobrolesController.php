<?php namespace App\modules\backend\cms\Controllers;

		    use App\Http\Requests;
			use App\Http\Controllers\Controller;
			use App\Http\Models\Jobrole;
			use Validator;
			use Redirect;
			use Request;

			class JobrolesController extends Controller {

			/**
			 * Display a listing of the resource.
			 *
			 * @return Response
			 */
			public function __construct()
			{
				$this->middleware('adminauth');
			}

			public function index()
			{ 
				if(is_axios()==true)
				{
					//filter params 
					$inputs = Request::all();
					$inputs = array_filter($inputs);
				$role_name = @$inputs['role_name'];
				$role_category = @$inputs['role_category'];
				$status = @$inputs['status'];
				$jobroles = Jobrole::where(array())
				->when($role_name, function ($query) use ($role_name) {
				        	return $query->where('role_name','LIKE' ,$role_name.'%');})
				->when($role_category, function ($query) use ($role_category) {
				        	return $query->where('role_category','LIKE' ,$role_category.'%');})
				->when($status, function ($query) use ($status) {
				        	return $query->where('status','LIKE' ,$status.'%');})
				->paginate(20);
			      $jobroles->setPath('jobroles');
					$jobroles->appends(Request::except('page'));
					return view('cms::jobroles.index_view')->with('jobroles', $jobroles);

				}
				return view('cms::jobroles.index');
			}

			/**
			 * Show the form for creating a new resource.
			 *
			 * @return Response
			 */
			public function create()
			{
				//
				return view('cms::jobroles.create');
			}

			/**
			 * Store a newly created resource in storage.
			 *
			 * @return Response
			 */
			public function store()
			{
				//
				// validate
		        // read more on validation at http://laravel.com/docs/validation
		        	$status = 200;
			        $response = array();
			        $message = '';

		        
				$rules = array('role_name'=>'required',
			        'role_category'=>'required',
			        'status'=>'required',
			        );

				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        
		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		        	$status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		            
		        } else {
		            // store
		            $jobroles = new Jobrole;
		            $jobroles->role_name = $inputs['role_name'];
				        		$jobroles->role_category = $inputs['role_category'];
				        		$jobroles->status = $inputs['status'];
				        		$jobroles->save();

		           //trigger seo url
					triggerSeoUrls($jobroles->id,'jobrole',$inputs['main_seo_title']);
						$message = 'Successfully created Jobroles!';

		            
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Display the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function show($id)
			{
				//
				if($id=='quickupdate')
				{
					return $this->quickUpdate();
				}
				
				 $jobroles = Jobrole::find($id);

				 if(empty($jobroles))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;
				 	
				 }

		        // show the view and pass the jobroles to it
		        return view('cms::jobroles.show')->with('jobroles', $jobroles);
			}


			//to update status quickly

			public function quickUpdate()
			{
				$inputs = Request::all();
				if(isset($inputs['id']) && isset($inputs['action']))
				{
					if($inputs['action']==1){$action='ACTIVE';}
					else if($inputs['action']==2){$action='INACTIVE';}
					else if($inputs['action']==3){$action='DELETED';}
					if($action!='')
					{Jobrole::whereIn('id',explode(',', $inputs['id']))->update(['status'=>$action]);}
				}
			}

			/**
			 * Show the form for editing the specified resource.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function edit($id)
			{
				//
				$jobroles = Jobrole::find($id);
				if(empty($jobroles))
				 {
				 	echo 'Oops! Id doesn\'t exists';exit;

				 }

		        // show the view and pass the jobroles to it
		        return view('cms::jobroles.edit')->with('jobroles', $jobroles);
			}

			/**
			 * Update the specified resource in storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function update($id)
			{
				// read more on validation at http://laravel.com/docs/validation
		       		$status = 200;
			        $response = array();
			        $message = '';
		       $rules = array();
				$inputs = Request::all();
				$inputs = array_filter($inputs);

		        $validator = Validator::make(Request::all(), $rules);

		        // process the login
		        if ($validator->fails()) {
		            $status = 422;
		        	$response['errors'] = laravel_error_parser($validator->errors()->toArray());
		        	$message = 'Validation Errors';
		        } else {
		            // store
		             $jobroles = Jobrole::find($id);$jobroles->role_name = $inputs['role_name'];
				        		$jobroles->role_category = $inputs['role_category'];
				        		$jobroles->status = $inputs['status'];
				        		$jobroles->save();

		           //trigger seo url
					triggerSeoUrls($jobroles->id,'jobrole',$inputs['main_seo_title']);
						$message = 'Successfully updated Jobroles!';
		           
		        }
		        return $this->response($response,$status,$message);
			}

			/**
			 * Remove the specified resource from storage.
			 *
			 * @param  int  $id
			 * @return Response
			 */
			public function destroy($id)
			{
				//
					$status = 200;
			        $response = array();
			        $message = '';
				$jobroles = Jobrole::find($id);
				if(empty($jobroles))
				 { 
				 	$status=421; 
				 	$response['errors']=array('No Jobroles found!');
				 	$message='No Jobroles found!';
				 }
				 else
				 {
				 	 $jobroles->delete();
				 	 $message = 'Successfully deleted Jobroles!';

				 }
				 return $this->response($response,$status,$message);
			 
			}

		}
		