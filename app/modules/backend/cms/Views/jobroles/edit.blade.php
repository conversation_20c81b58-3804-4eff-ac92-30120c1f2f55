
<div class="row">
		    <div class="col-md-8 col-md-offset-2">
		    <div class="panel panel-default panel-filled">
                <div class="panel-heading">
                  <h3 class="panel-title custom-font">Edit Jobrole</h3>
                </div>
                <div class="panel-body">

<form class="form-horizontal" action="{{ url('/'.getCurrentUrlPrefix().'/'.$jobroles->id) }}" method="POST" enctype="multipart/form-data" id="editForm">
            {{ method_field('PUT') }} 
            <input type="hidden" name="_token" value="{{ csrf_token() }}"><div class="form-group">
                  				<label for="role_name" class="col-sm-2 control-label">Role Name</label>
		                                <div class="col-sm-10"><input type="text" name="role_name" id="role_name" value="{{$jobroles->role_name}}" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
                  				<label for="role_category" class="col-sm-2 control-label">Role Category</label>
		                                <div class="col-sm-10"><input type="text" name="role_category" id="role_category" value="{{$jobroles->role_category}}" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
                  				<label for="status" class="col-sm-2 control-label">Status</label>
		                                <div class="col-sm-10">
		                                <select class="form-control mb-10" name="status" id="status">
					                      <option value="">Select status</option>
					                      @if($jobroles->status=="Active")
					                      <option value="Active" selected>Active</option>
					                      <option value="Inactive">Inactive</option>
					                      
					                      @elseif($jobroles->status=="Inactive")
					                      <option value="Active">Active</option>
					                      <option value="Inactive" selected>Inactive</option>
					                      @endif
					                    </select>
		                                </div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					           <p class="stdformbutton">
                                <button type="submit" class="btn btn-primary">Submit</button>
                                <button type="reset" class="btn">Reset</button>
                            </p>
                        </form>
                        <script src="{{asset('/ba/assets/js/common.js')}}"></script> 
                         <script type="text/javascript">
						  var arr_error_label = ['role_name','role_category','status',];
						   if (typeof(arr_error_label) !== 'undefined') 
						    {addErrorLabel(arr_error_label);} 
						 </script>  
                   </div>
                   </div>
                   </div>
                   </div>
                    