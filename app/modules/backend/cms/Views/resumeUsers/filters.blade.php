<div class="row">
   <div class="col-md-12">
      <section class="tile filterblock">
         <div class="tile-header dvd dvd-btm">
            <h1 class="custom-font">Resume Users data filter
            </h1>
         </div>
         <div class="tile-body">
            <form class="form-inline" role="form" id="filterForm" action="{{url('/'.getCurrentUrlPrefix())}}">
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="name" name="name" value="{{Request::get("name")}}" placeholder="Enter User Name" title="Enter User Name">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="email" name="email" value="{{Request::get("email")}}" placeholder="Enter User Email" title="Enter User Email">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="source" name="source" value="{{Request::get("source")}}" placeholder="Enter User Source" title="Enter User Source">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="utm_source" name="utm_source" value="{{Request::get("utm_source")}}" placeholder="Enter UTM Source" title="Enter UTM Source">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="utm_medium" name="utm_medium" value="{{Request::get("utm_medium")}}" placeholder="Enter UTM Medium" title="Enter UTM Medium">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="utm_campaign" name="utm_campaign" value="{{Request::get("utm_campaign")}}" placeholder="Enter UTM Campaign" title="Enter UTM Campaign">
               </div>
          
               <div class="form-group col-md-6 dflex">
                  <div class="input-group date w-360 mr-5" id='reg_date_picker_start'>
                     <input type="text" class="form-control" id="reg_start_time" data-filter_reg_start_date="{{\Request::input('reg_start_time')}}" name="reg_start_time" autocomplete="off" placeholder="Registration DateTime Start"/>
                     <span class="input-group-addon"> <span class="fa fa-calendar"></span> </span> 
                  </div>
                  <div class="input-group date w-360" id='reg_date_picker_end'>
                     <input type="text" class="form-control" id="reg_end_time" data-filter_reg_end_date="{{\Request::input('reg_end_time')}}" name="reg_end_time" autocomplete="off" placeholder="Registration DateTime End"/>
                     <span class="input-group-addon"> <span class="fa fa-calendar"></span> </span> 
                  </div>
               </div>
               <div class="form-group col-md-6 dflex">
                  <div class="input-group date w-360 mr-5" id='login_date_picker_start'>
                     <input type="text" class="form-control" id="login_start_time" data-filter_login_start_date="{{\Request::input('login_start_time')}}" name="login_start_time" autocomplete="off" placeholder="Last Login DateTime Start"/>
                     <span class="input-group-addon"> <span class="fa fa-calendar"></span> </span> 
                  </div>
                  <div class="input-group date w-360" id='login_date_picker_end'>
                     <input type="text" class="form-control" id="login_end_time" data-filter_login_end_date="{{\Request::input('login_end_time')}}" name="login_end_time" autocomplete="off" placeholder="Last Login DateTime End"/>
                     <span class="input-group-addon"> <span class="fa fa-calendar"></span> </span> 
                  </div>
               </div>
               
               <button type="submit" class="btn btn-primary">Search</button>
               <button type="reset" class="btn btn-default">Reset</button>
            </form>
         </div>
      </section>
   </div>
</div>

<link rel="stylesheet" href="{{asset('/ba/assets/js/vendor/datetimepicker/css/bootstrap-datetimepicker.min.css')}}">
<script src="{{asset('/ba/assets/js/vendor/datetimepicker/js/bootstrap-datetimepicker.min.js')}}"></script>
<script src="{{asset('/ba/assets/js/rkcommon.js')}}"></script>

<script>
var filter_reg_start_time = $('#reg_start_time').attr("data-filter_reg_start_date");
var filter_reg_end_time = $('#reg_end_time').attr("data-filter_reg_end_date");

var filter_login_start_time = $('#login_start_time').attr("data-filter_login_start_date");
var filter_login_end_time = $('#login_end_time').attr("data-filter_login_end_date");

$(function () {
    var reg_start_date = createDateString(filter_reg_start_time);
    var reg_end_date = createDateString(filter_reg_end_time);

    $("#reg_start_time").val(reg_start_date);
    $("#reg_end_time").val(reg_end_date);
    
    $('#reg_date_picker_start').datetimepicker({
      format:'YYYY-MM-DD HH:mm:ss',
    });
    $('#reg_date_picker_end').datetimepicker({
      format:'YYYY-MM-DD HH:mm:ss',
      useCurrent: false //Important! See issue #1075
    });
    $("#reg_date_picker_start").on("dp.change", function (e) {
      $('#reg_date_picker_end').data("DateTimePicker").minDate(e.date);
    });
    $("#reg_date_picker_end").on("dp.change", function (e) {
      $('#reg_date_picker_start').data("DateTimePicker").maxDate(e.date);
    });

   var login_start_date = createDateString(filter_login_start_time);
   var login_end_date = createDateString(filter_login_end_time);

   $("#login_start_time").val(login_start_date);
   $("#login_end_time").val(login_end_date);
   
   $('#login_date_picker_start').datetimepicker({
   format:'YYYY-MM-DD HH:mm:ss',
   });
   $('#login_date_picker_end').datetimepicker({
   format:'YYYY-MM-DD HH:mm:ss',
   useCurrent: false //Important! See issue #1075
   });
   $("#login_date_picker_start").on("dp.change", function (e) {
   $('#login_date_picker_end').data("DateTimePicker").minDate(e.date);
   });
   $("#login_date_picker_end").on("dp.change", function (e) {
   $('#login_date_picker_start').data("DateTimePicker").maxDate(e.date);
   });
});
</script>
