
		    <div class="row">
		    <div class="col-md-8 col-md-offset-2">
		    <div class="panel panel-default panel-filled">
                <div class="panel-heading">
                  <h3 class="panel-title custom-font">Add New Jobroleprompt</h3>
                </div>
                <div class="panel-body">
		   
		    <form class="form-horizontal" role="form" method="post" id="createForm" action="{{url('/'.getCurrentUrlPrefix())}}" enctype="multipart/form-data">
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                    <div class="form-group">
                  				<label for="jobrole_id" class="col-sm-2 control-label">Jobrole Id</label>
		                                <div class="col-sm-10"><input type="text" name="jobrole_id" id="jobrole_id" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
                  				<label for="exp_min_in_year" class="col-sm-2 control-label">Exp Min In Year</label>
		                                <div class="col-sm-10"><input type="text" name="exp_min_in_year" id="exp_min_in_year" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
                  				<label for="exp_max_in_year" class="col-sm-2 control-label">Exp Max In Year</label>
		                                <div class="col-sm-10"><input type="text" name="exp_max_in_year" id="exp_max_in_year" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
				        		  <label for="prompt1" class="col-sm-2 control-label">Prompt1</label>
                                <div class="col-sm-10"><textarea cols="80" rows="5" name="prompt1" id="prompt1" class="form-control"></textarea></div>
                                </div>
			        			<hr class="line-dashed line-full"/>
			        			<div class="form-group">
				        		  <label for="prompt2" class="col-sm-2 control-label">Prompt2</label>
                                <div class="col-sm-10"><textarea cols="80" rows="5" name="prompt2" id="prompt2" class="form-control"></textarea></div>
                                </div>
			        			<hr class="line-dashed line-full"/>
			        			<div class="form-group">
				        		  <label for="prompt3" class="col-sm-2 control-label">Prompt3</label>
                                <div class="col-sm-10"><textarea cols="80" rows="5" name="prompt3" id="prompt3" class="form-control"></textarea></div>
                                </div>
			        			<hr class="line-dashed line-full"/>
			        			   <p class="stdformbutton">
                                <button type="submit" class="btn btn-primary">Submit</button>
                                <button type="reset" class="btn">Reset</button>
                            </p>
                        </form>
                        <script src="{{asset('/ba/assets/js/common.js')}}"></script> 
                         <script type="text/javascript">
						  var arr_error_label = ['jobrole_id','exp_min_in_year','exp_max_in_year',];
						   if (typeof(arr_error_label) !== 'undefined') 
						    {addErrorLabel(arr_error_label);} 
						 </script>  
                    </div>
                    </div>
                    </div>
                    </div>