
		    <div class="row">
		    <div class="col-md-8 col-md-offset-2">
		    <div class="panel panel-default panel-filled">
                <div class="panel-heading">
                  <h3 class="panel-title custom-font">Add New Uprofile</h3>
                </div>
                <div class="panel-body">
		   
		    <form class="form-horizontal" role="form" method="post" id="createForm" action="{{url('/'.getCurrentUrlPrefix())}}" enctype="multipart/form-data">
                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                    <div class="form-group">
                  				<label for="user_id" class="col-sm-2 control-label">User Id</label>
		                                <div class="col-sm-10"><input type="text" name="user_id" id="user_id" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
                  				<label for="job role_id" class="col-sm-2 control-label">Job Role Id</label>
		                                <div class="col-sm-10"><input type="text" name="job role_id" id="job role_id" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
                  				<label for="exp_in_year" class="col-sm-2 control-label">Exp In Year</label>
		                                <div class="col-sm-10"><input type="text" name="exp_in_year" id="exp_in_year" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
                  				<label for="is_ai_used" class="col-sm-2 control-label">Is Ai Used</label>
		                                <div class="col-sm-10"><input type="text" name="is_ai_used" id="is_ai_used" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
                  				<label for="mobile_number" class="col-sm-2 control-label">Mobile Number</label>
		                                <div class="col-sm-10"><input type="text" name="mobile_number" id="mobile_number" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
                  				<label for="current_company" class="col-sm-2 control-label">Current Company</label>
		                                <div class="col-sm-10"><input type="text" name="current_company" id="current_company" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					        <div class="form-group">
                  				<label for="current_role" class="col-sm-2 control-label">Current Role</label>
		                                <div class="col-sm-10"><input type="text" name="current_role" id="current_role" class="form-control"></div>
		                                </div>
					        <hr class="line-dashed line-full"/>
					           <p class="stdformbutton">
                                <button type="submit" class="btn btn-primary">Submit</button>
                                <button type="reset" class="btn">Reset</button>
                            </p>
                        </form>
                        <script src="{{asset('/ba/assets/js/common.js')}}"></script> 
                         <script type="text/javascript">
						  var arr_error_label = ['user_id','job role_id','exp_in_year','is_ai_used','mobile_number','current_company','current_role',];
						   if (typeof(arr_error_label) !== 'undefined') 
						    {addErrorLabel(arr_error_label);} 
						 </script>  
                    </div>
                    </div>
                    </div>
                    </div>