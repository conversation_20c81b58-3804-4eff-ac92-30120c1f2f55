<div class="row">
    <div class="col-md-12">
       <section class="tile filterblock">
          <div class="tile-header dvd dvd-btm">
             <h1 class="custom-font">Data filter
             </h1>
          </div>
          <div class="tile-body">
             <form class="form-inline" role="form" id="filterForm" action="{{route('traffic.analyzer')}}">
               
                <div class="form-group col-md-2">

                   <input type="text" class="form-control" id="sdomain" name="domain[]" value="" placeholder="Enter Domain Name" title="Domain Name">
                </div>
                <div class="form-group col-md-2">
                   <input type="text" class="form-control" id="srequest_date" name="request_date" value="{{Request::get('request_date')}}" placeholder="Date eg. 01/Mar/2024 " title="Enter Date">
                </div>
                <button type="submit" class="btn btn-primary">Search</button>
                <button type="reset" class="btn btn-default">Reset</button>
             </form>
          </div>
          @if(isset($data_filters) && !empty($data_filters))
          <div class="btn-toolbar" role="toolbar" >
            <span class="hidden" id="main_filters" data-filter_data="{{@json_encode($data_filters)}}"></span>
            @foreach ($data_filters as $filter_key=>$filter_val)
                @php $filter_val =  (is_array($filter_val)) ? implode(", ",$filter_val) : $filter_val; @endphp
                <div class="btn-group mr-2" role="group">
                    <button type="button" class="btn btn-info" disabled>{{$filter_key.'='.$filter_val }}</button>
                    <button type="button" class="btn btn-secondary remove_analyzer_filter" data-key="{{$filter_key}}" data-value="{{$filter_val}}">X</button>
                </div>
            @endforeach
          </div>

          @endif
       </section>
    </div>
 </div>


<div class="row">
    
    @if(isset($filters) && !empty($filters))
        @foreach($filters as $fkey => $fvalue)
            @if(isset($showfields[$fkey]))

            <div class="col-md-4 col-xs-12">
                <section class="tile">
                    <div class="tile-header dvd dvd-btm">
                        <h1 class="custom-font">{{$showfields[$fkey]}} Data</h1>
                    </div>
                    @if (Session::has('message'))
                    <div class="alert alert-info">{{ Session::get('message') }}</div>
                    @endif
                    <div class="tile-body">
                        <div class="table-responsive">
                            <input class="tf_analyzer_box_search form-control" placeholder="{{'Search '.$showfields[$fkey]}}">
                            <table class="table mb-0" id="">
                                <thead>
                                    <tr>
                                        <th class="head0 sortby" sortby="title">Name</th>
                                        <th class="head0 sortby" sortby="title">Count</th>
                                    </tr>
                                </thead>
                                <tbody>
                                        @foreach($fvalue as $key => $value)
                                            <tr>
                                                <td><a role="button" class="traffic_analyze_table" data-box-key="{{$fkey.'[]='.$key}}">{{$key}}</a></td>
                                                <td>{{$value }}</td>
                                            </tr>
                                        @endforeach
                                    
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
            </div>
            @endif
        @endforeach
    @endif

    
 </div>
<script src="{{asset('/ba/assets/js/common.js')}}"></script> 

<style>
    .table-responsive{
        max-height: 300px;
        overflow-x: scroll;
    }
    .badge-pill{
        font-size: 14px;
    }
    .badge {
        padding: 5px 9px;
    }
</style>
