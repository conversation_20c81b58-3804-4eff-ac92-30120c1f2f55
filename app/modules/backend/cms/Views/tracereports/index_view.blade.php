 @include('cms::tracereports.filters')

    @if(isset($list_consolidate) && !empty($list_consolidate))
      <div class="row">
        <div class="col-md-6">
          <section class="tile">
            <div class="tile-header dvd dvd-btm">
              <h1 class="custom-font"><strong>Progress</strong> Chart</h1>
              <ul class="controls">
                <li class="dropdown"> <a role="button" tabindex="0" class="dropdown-toggle settings" data-toggle="dropdown"> <i class="fa fa-cog"></i> <i class="fa fa-spinner fa-spin"></i> </a> </li>
                <li> <a role="button" tabindex="0" class="tile-toggle"> <span class="minimize"><i class="fa fa-angle-down"></i></span> <span class="expand"><i class="fa fa-angle-up"></i></span> </a> </li>
                <li> <a role="button" tabindex="0" class="tile-refresh" title=" Refresh"> <i class="fa fa-refresh"></i> </a> </li>
                <li> <a role="button" tabindex="0" class="tile-fullscreen" title="Fullscreen"> <i class="fa fa-expand"></i> </a> </li>
                <li class="remove"><a role="button" tabindex="0" class="tile-close"><i class="fa fa-times"></i></a></li>
              </ul>
            </div>
            <div class="tile-body">
              <div id="line-chart" style="height: 250px"></div>
            </div>
          </section>
        </div>
        <div class="col-md-6">
          <section class="tile">
            <div class="tile-header dvd dvd-btm">
              <h1 class="custom-font"><strong>Consolidate </strong>Chart</h1>
              <ul class="controls">
                <li class="dropdown"> <a role="button" tabindex="0" class="dropdown-toggle settings" data-toggle="dropdown"> <i class="fa fa-cog"></i> <i class="fa fa-spinner fa-spin"></i> </a> </li>
                <li> <a role="button" tabindex="0" class="tile-toggle"> <span class="minimize"><i class="fa fa-angle-down"></i></span> <span class="expand"><i class="fa fa-angle-up"></i></span> </a> </li>
                <li> <a role="button" tabindex="0" class="tile-refresh" title=" Refresh"> <i class="fa fa-refresh"></i> </a> </li>
                <li> <a role="button" tabindex="0" class="tile-fullscreen" title="Fullscreen"> <i class="fa fa-expand"></i> </a> </li>
                <li class="remove"><a role="button" tabindex="0" class="tile-close"><i class="fa fa-times"></i></a></li>
              </ul>
            </div>
            <div class="tile-body">
              <div id="pie-chart" style="height: 250px"></div>
            </div>
          </section>
        </div>
      </div>
    @endif
		      <div class="row">
                  <div class="col-md-12">
                     <section class="tile">
                        <div class="tile-header dvd dvd-btm">
                           <h1 class="custom-font">View All Nodes</h1>
			 <button onclick="javascript:tableToCSV();" name="Download">Download CSV</button>
                        </div>
                        @if (Session::has('message'))
                        <div class="alert alert-info">{{ Session::get('message') }}</div>
                        @endif
                        <div class="tile-body">
                           <div class="table-responsive">
                              <table class="table mb-0" id="reports_table">
                                 <thead>
                                    <tr>
                                       <th class="report_data_row head0 sortby" sortby="title">Date</th>
                                       <th class="report_data_row head0 sortby" sortby="title">Domain</th>
                                       <th class="report_data_row head0 sortby" sortby="title">Direct Hits</th>
                                       <th class="report_data_row head0 sortby" sortby="title">Whitelist Hits</th>
                                       <th class="report_data_row head0 sortby" sortby="title">Others Hits</th>
                                       <th class="report_data_row head0 sortby" sortby="title">Total Hits</th>
                                       <th class="report_data_row head0 sortby" sortby="title">Breakup</th>
                                    </tr>
                                 </thead>
                                 <tbody>
                                    @foreach($tracereports as $key => $report)
                                    <tr>
                                       <td class='report_data_row' >{{ @$report['date_wise'] }}</td>
                                       <td class='report_data_row' >{{ @$report['domain_name'] }}</td>
                                       <td class='report_data_row' >{{ @$report['direct_hits'] }}</td>
                                       <td class='report_data_row' >{{ @$report['whitelist_hits'] }}</td>
                                       <td class='report_data_row' >{{ @$report['others_hits'] }}</td>
                                       <td class='report_data_row' >{{ @$report['total_hits'] }}</td>
                                       
                                        <td class="report_data_row">
                                            @php
                                                $hits = json_decode($report['hits_breakdown'],true);
                                                arsort($hits['SOURCE']);
                                            @endphp
                                            @foreach($hits['SOURCE'] as $hits_key => $hits_value)
                                                {{$hits_key.' : '.$hits_value}}<br>
                                            @endforeach
                                        </td>
                                      
 
                                      
                                    </tr>
                                    @endforeach
                                 </tbody>
                              </table>
                           </div>
                        </div>
                     </section>
                  </div>
               </div>
			    <script src="{{asset('/ba/assets/js/common.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/bootstrap/bootstrap.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/jRespond/jRespond.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/sparkline/jquery.sparkline.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/slimscroll/jquery.slimscroll.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/animsition/js/jquery.animsition.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/screenfull/screenfull.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/flot/jquery.flot.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/flot/jquery.flot.resize.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/flot/jquery.flot.orderBars.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/flot/jquery.flot.stack.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/flot/jquery.flot.pie.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/flot-spline/jquery.flot.spline.min.js')}}"></script> 
<script src="{{asset('/ba/assets/js/vendor/flot-tooltip/jquery.flot.tooltip.min.js')}}"></script> 
            <!--  Custom JavaScripts --> 
<!-- <script src="{{asset('/ba/assets/js/main.js')}}"></script>  -->
<!--/ custom javascripts --> 
 @if(isset($list_consolidate) && !empty($list_consolidate))

 <?php
$data_set = objToArray($list_consolidate,'date_wise');
$direct_hits = '';
$whitelist_hits = '';
$others_hits = '';
$dates_list = '';
$i=1;
foreach($data_set as $k=>$ds)
{
    $direct_hits .= '['.$i.','.$ds['total_direct_hits'].'],';
    $whitelist_hits .= '['.$i.','.$ds['total_whitelist_hits'].'],';
    $others_hits .= '['.$i.','.$ds['total_others_hits'].'],';

    $dates_list .= '[ '.$i.', \''.$k.'\' ],';
    //$dates_list[$i] = $k;
    $i++;

}
$direct_hits = rtrim($direct_hits,',');
$whitelist_hits = rtrim($whitelist_hits,',');
$others_hits = rtrim($others_hits,',');
$dates_list = rtrim($dates_list,',');

//2nd graph

$date_wise_input = Request::get("date_wise");

$consolidate = @$data_set[$date_wise_input];

 ?>
<!-- Page Specific Scripts  --> 
<script type="text/javascript">
        // $(window).load(function(){
            // Initialize Line Chart
            var data1 = [{
                data: [{{$direct_hits}}],
                label: 'Direct',
                points: {
                    show: true,
                    radius: 6
                },
                splines: {
                    show: true,
                    tension: 0.45,
                    lineWidth: 5,
                    fill: 0
                } 
            }, {
                data: [{{$whitelist_hits}}],
                label: 'Whitelisted',
                points: {
                    show: true,
                    radius: 6
                },
                splines: {
                    show: true,
                    tension: 0.45,
                    lineWidth: 5,
                    fill: 0
                }
            },{
                 data: [{{$others_hits}}],
                label: 'Others',
                points: {
                    show: true,
                    radius: 6
                },
                splines: {
                    show: true,
                    tension: 0.45,
                    lineWidth: 5,
                    fill: 0
                }
            }
            ];

            var options1 = {
                colors: ['#a2d200', '#cd97eb','#000'],
                series: {
                    shadowSize: 0
                },
                xaxis:{
                    font: {
                        color: '#3d4c5a'
                    },
                    position: 'bottom',
                    ticks: [
                       {!!$dates_list!!}
                    ]
                },
                yaxis: {
                    font: {
                        color: '#3d4c5a'
                    }
                },
                grid: {
                    hoverable: true,
                    clickable: true,
                    borderWidth: 0,
                    color: '#ccc'
                },
                tooltip: true,
                tooltipOpts: {
                    content: '%s: %y.0',
                    defaultTheme: false,
                    shifts: {
                        x: 0,
                        y: 20
                    }
                }
            };

            var plot1 = $.plot($("#line-chart"), data1, options1);

            $(window).resize(function() {
                // redraw the graph in the correctly sized div
                plot1.resize();
                plot1.setupGrid();
                plot1.draw();
            });
            // * Initialize Line Chart
                        
            // Initialize Pie Chart
            var data6 = [
                { label: 'Direct', data: {{@$consolidate['total_direct_hits']}} },
                { label: 'Whitelisted', data: {{@$consolidate['total_whitelist_hits']}} },
                { label: 'Others', data: {{@$consolidate['total_others_hits']}} },
               
            ];

            var options6 = {
                series: {
                    pie: {
                        show: true,
                        innerRadius: 0,
                        stroke: {
                            width: 0
                        },
                        label: {
                            show: true,
                            threshold: 0.02
                        }
                    }
                },
                colors: ['#46bc9f','#e56b6b','#ecc755'],
                grid: {
                    hoverable: true,
                    clickable: true,
                    borderWidth: 0,
                    color: '#3d4c5a'
                },
                tooltip: true,
                tooltipOpts: { content: '%s: %p.0%' }
            };

            var plot6 = $.plot($("#pie-chart"), data6, options6);

            $(window).resize(function() {
                // redraw the graph in the correctly sized div
                plot6.resize();
                plot6.setupGrid();
                plot6.draw();
            });
            // * Initialize Pie Chart

          
        // });
    </script> 
<!--/ Page Specific Scripts --> 
@endif
<script type="text/javascript">
    
function tableToCSV() {
 
            // Variable to store the final csv data
            var csv_data = [];
 
            // Get each row data
            var rows = document.getElementById('reports_table').getElementsByTagName('tr');            
            for (var i = 0; i < rows.length; i++) {
 
                // Get each column data
                var cols = rows[i].querySelectorAll('.report_data_row'); 

                // Stores each csv row data
                var csvrow = [];
                for (var j = 0; j < cols.length; j++) {
 
                    // Get the text data of each cell
                    // of a row and push it to csvrow
                    if(j==6){
                        // console.log('data',cols[j].innerText)
                        csvrow.push("\""+cols[j].innerText+"\"");
                    }else{
                        csvrow.push(cols[j].innerHTML);
                    }
                }
 
                // Combine each column value with comma
                csv_data.push(csvrow.join(","));
            }
 
            // Combine each row data with new line character
            csv_data = csv_data.join('\n');
 
            // Call this function to download csv file 
            downloadCSVFile(csv_data);
 
        }
 
        function downloadCSVFile(csv_data) {
 
            // Create CSV file object and feed
            // our csv_data into it
            CSVFile = new Blob([csv_data], {
                type: "text/csv"
            });
 
            // Create to temporary link to initiate
            // download process
            var temp_link = document.createElement('a');
 
            // Download csv file
            temp_link.download = "reports.csv";
            var url = window.URL.createObjectURL(CSVFile);
            temp_link.href = url;
 
            // This link should not be displayed
            temp_link.style.display = "none";
            document.body.appendChild(temp_link);
 
            // Automatically click the link to
            // trigger download
            temp_link.click();
            document.body.removeChild(temp_link);
        }
    </script>



