 @include('cms::resumelists.filters')
 <div class="row">
     <div class="col-md-12">
         <section class="tile">
             <div class="tile-header dvd dvd-btm">
                 <h1 class="custom-font">View All Nodes</h1>
                 <button onclick="javascript:tableToCSV();" name="Download">Download CSV</button>
             </div>
             @if (Session::has('message'))
                 <div class="alert alert-info">{{ Session::get('message') }}</div>
             @endif
             <div class="tile-body" style="overflow-x: auto;">
                 <div class="table-responsive">
                     <table class="table mb-0" id="reports_table">
                         <thead>
                             <tr>
                                 <th class="report_data_row head0 sortby" sortby="title">User Name</th>
                                 <th class="report_data_row head0 sortby" sortby="title">title</th>
                                 <th class="report_data_row head0 sortby" sortby="title">template</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Email</th>
                                 <th class="report_data_row head0 sortby" sortby="title">GitHub</th>
                                 <th class="report_data_row head0 sortby" sortby="title">LinkedIn</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Phone</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Location</th>
                                 <th class="report_data_row head0 sortby" sortby="title">JobTitle</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Objective</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Skills</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Education</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Projects</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Work Exp.</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Languages</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Certifications</th>
                                 <th class="report_data_row head0 sortby" sortby="title">Custom Sections</th>
                                 <th class="report_data_row head0 sortby" sortby="title">createdAt</th>
                                 <th class="report_data_row head0 sortby" sortby="title">updatedAt</th>
                             </tr>
                         </thead>
                         <tbody>
                             @foreach ($resumelists as $key => $report)
                                 <tr>
                                     <td class='report_data_row'>{{ @$report->Name }}</td>
                                     <td class='report_data_row'>{{ @$report->title }}</td>
                                     <td class='report_data_row'>{{ @$report->template }}</td>
                                     <td class='report_data_row'>{{ @$report->Email }}</td>
                                     <td class='report_data_row'>{{ @$report->GitHub }}</td>
                                     <td class='report_data_row'>{{ @$report->LinkedIn }}</td>
                                     <td class='report_data_row'>{{ @$report->Phone }}</td>
                                     <td class='report_data_row'>{{ @$report->Location }}</td>
                                     <td class='report_data_row'>{{ @$report->JobTitle }}</td>
                                     <td class='report_data_row'>{{ @$report->Objective }}</td>
                                     <td class='report_data_row'>{{ @$report->Skills }}</td>
                                     <td class='report_data_row'>{{ @$report->Education }}</td>
                                     <td class='report_data_row'>{{ @$report->Projects }}</td>
                                     <td class='report_data_row'>{{ @$report->WorkExperience }}</td>
                                     <td class='report_data_row'>{{ @$report->Languages }}</td>
                                     <td class='report_data_row'>{{ @$report->Certifications }}</td>
                                     <td class='report_data_row'>{{ @$report->CustomSections }}</td>
                                     <td class='report_data_row'>{{ @$report->createdAt }}</td>
                                     <td class='report_data_row'>{{ @$report->updatedAt }}</td>
                                 </tr>
                             @endforeach
                         </tbody>
                     </table>
                 </div>
             </div>
         </section>
     </div>
 </div>
 <div class="tile-footer dvd dvd-top">
     <div class="row">
         {{-- <div class="col-sm-3 hidden-xs">
             <select class="input-sm form-control w-sm inline bulk_action_type">
                 <option value="">Select Action</option>
                 <option value="1">Mark Active</option>
                 <option value="2">Mark Inactive</option>
                 <option value="3">Mark Deleted</option>

             </select>
             <button class="btn btn-sm btn-default br-3 bulk_action_trigger">Apply</button>
         </div> --}}
         <div class="col-sm-4 text-left"><small class="text-muted">Total {{ $resumelists->total() }} records
                 found!</small></div>
         <div class="col-sm-5 text-right">
             <ul class="pagination pagination-sm m-0">
                 <?php echo $resumelists->render(); ?>
             </ul>

         </div>
     </div>
 </div>

 <script src="{{ asset('/ba/assets/js/common.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/bootstrap/bootstrap.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/jRespond/jRespond.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/sparkline/jquery.sparkline.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/slimscroll/jquery.slimscroll.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/animsition/js/jquery.animsition.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/screenfull/screenfull.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/flot/jquery.flot.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/flot/jquery.flot.resize.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/flot/jquery.flot.orderBars.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/flot/jquery.flot.stack.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/flot/jquery.flot.pie.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/flot-spline/jquery.flot.spline.min.js') }}"></script>
 <script src="{{ asset('/ba/assets/js/vendor/flot-tooltip/jquery.flot.tooltip.min.js') }}"></script>

 <!--  Custom JavaScripts -->
 <script type="text/javascript">
     function tableToCSV() {

         // Variable to store the final csv data
         var csv_data = [];

         // Get each row data
         var rows = document.getElementById('reports_table').getElementsByTagName('tr');
         for (var i = 0; i < rows.length; i++) {

             // Get each column data
             var cols = rows[i].querySelectorAll('.report_data_row');

             // Stores each csv row data
             var csvrow = [];
             for (var j = 0; j < cols.length; j++) {

                 // Get the text data of each cell
                 // of a row and push it to csvrow
                 if (j == 6) {
                     // console.log('data',cols[j].innerText)
                     csvrow.push("\"" + cols[j].innerText + "\"");
                 } else {
                     csvrow.push(cols[j].innerHTML);
                 }
             }

             // Combine each column value with comma
             csv_data.push(csvrow.join(","));
         }

         // Combine each row data with new line character
         csv_data = csv_data.join('\n');

         // Call this function to download csv file 
         downloadCSVFile(csv_data);

     }

     function downloadCSVFile(csv_data) {

         // Create CSV file object and feed
         // our csv_data into it
         CSVFile = new Blob([csv_data], {
             type: "text/csv"
         });

         // Create to temporary link to initiate
         // download process
         var temp_link = document.createElement('a');

         // Download csv file
         temp_link.download = "Resume_lists.csv";
         var url = window.URL.createObjectURL(CSVFile);
         temp_link.href = url;

         // This link should not be displayed
         temp_link.style.display = "none";
         document.body.appendChild(temp_link);

         // Automatically click the link to
         // trigger download
         temp_link.click();
         document.body.removeChild(temp_link);
     }
 </script>
