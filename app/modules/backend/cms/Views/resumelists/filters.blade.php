<div class="row">
   <div class="col-md-12">
      <section class="tile filterblock">
         <div class="tile-header dvd dvd-btm">
            <h1 class="custom-font">Resume Users data filter
            </h1>
         </div>
         <div class="tile-body">
            <form class="form-inline" role="form" id="filterForm" action="{{url('/'.getCurrentUrlPrefix())}}">
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="Name" name="Name" value="{{Request::get("Name")}}" placeholder="Enter User Name" title="Enter User Name">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="Email" name="Email" value="{{Request::get("Email")}}" placeholder="Enter User Email" title="Enter User Email">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="title" name="title" value="{{Request::get("title")}}" placeholder="Enter Resume title" title="Enter Resume title">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="template" name="template" value="{{Request::get("template")}}" placeholder="Enter template" title="Enter template">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="GitHub" name="GitHub" value="{{Request::get("GitHub")}}" placeholder="Enter GitHub" title="Enter GitHub">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="LinkedIn" name="LinkedIn" value="{{Request::get("LinkedIn")}}" placeholder="Enter LinkedIn" title="Enter LinkedIn">
               </div>

               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="Phone" name="Phone" value="{{Request::get("Phone")}}" placeholder="Enter User Phone" title="Enter User Phone">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="Location" name="Location" value="{{Request::get("Location")}}" placeholder="Enter User Location" title="Enter User Location">
               </div>
               <div class="form-group col-md-2">
                  <input type="text" class="form-control" id="JobTitle" name="JobTitle" value="{{Request::get("JobTitle")}}" placeholder="Enter User JobTitle" title="Enter User JobTitle">
               </div>

               <div class="form-group col-md-6 dflex">
                  <div class="input-group date w-360 mr-5" id='created_date_picker_start'>
                     <input type="text" class="form-control" id="created_start_time" data-filter_created_start_date="{{\Request::input('created_start_time')}}" name="created_start_time" autocomplete="off" placeholder="Created At DateTime Start"/>
                     <span class="input-group-addon"> <span class="fa fa-calendar"></span> </span> 
                  </div>
                  <div class="input-group date w-360" id='created_date_picker_end'>
                     <input type="text" class="form-control" id="created_end_time" data-filter_created_end_date="{{\Request::input('created_end_time')}}" name="created_end_time" autocomplete="off" placeholder="Created At DateTime End"/>
                     <span class="input-group-addon"> <span class="fa fa-calendar"></span> </span> 
                  </div>
               </div>
               <div class="form-group col-md-6 dflex">
                  <div class="input-group date w-360 mr-5" id='updated_date_picker_start'>
                     <input type="text" class="form-control" id="updated_start_time" data-filter_updated_start_date="{{\Request::input('updated_start_time')}}" name="updated_start_time" autocomplete="off" placeholder="Updated At DateTime Start"/>
                     <span class="input-group-addon"> <span class="fa fa-calendar"></span> </span> 
                  </div>
                  <div class="input-group date w-360" id='updated_date_picker_end'>
                     <input type="text" class="form-control" id="updated_end_time" data-filter_updated_end_date="{{\Request::input('updated_end_time')}}" name="updated_end_time" autocomplete="off" placeholder="Updated At DateTime End"/>
                     <span class="input-group-addon"> <span class="fa fa-calendar"></span> </span> 
                  </div>
               </div>
               
               <button type="submit" class="btn btn-primary">Search</button>
               <button type="reset" class="btn btn-default">Reset</button>
            </form>
         </div>
      </section>
   </div>
</div>

<link rel="stylesheet" href="{{asset('/ba/assets/js/vendor/datetimepicker/css/bootstrap-datetimepicker.min.css')}}">
<script src="{{asset('/ba/assets/js/vendor/datetimepicker/js/bootstrap-datetimepicker.min.js')}}"></script>
<script src="{{asset('/ba/assets/js/rkcommon.js')}}"></script>

<script>
var filter_created_start_time = $('#created_start_time').attr("data-filter_created_start_date");
var filter_created_end_time = $('#created_end_time').attr("data-filter_created_end_date");

var filter_updated_start_time = $('#updated_start_time').attr("data-filter_updated_start_date");
var filter_login_end_time = $('#updated_end_time').attr("data-filter_updated_end_date");

$(function () {
    var created_start_date = createDateString(filter_created_start_time);
    var created_end_date = createDateString(filter_created_end_time);

    $("#created_start_time").val(created_start_date);
    $("#created_end_time").val(created_end_date);
    
    $('#created_date_picker_start').datetimepicker({
      format:'YYYY-MM-DD HH:mm:ss',
    });
    $('#created_date_picker_end').datetimepicker({
      format:'YYYY-MM-DD HH:mm:ss',
      useCurrent: false //Important! See issue #1075
    });
    $("#created_date_picker_start").on("dp.change", function (e) {
      $('#created_date_picker_end').data("DateTimePicker").minDate(e.date);
    });
    $("#created_date_picker_end").on("dp.change", function (e) {
      $('#created_date_picker_start').data("DateTimePicker").maxDate(e.date);
    });

   var updated_start_date = createDateString(filter_updated_start_time);
   var updated_end_date = createDateString(filter_login_end_time);

   $("#updated_start_time").val(updated_start_date);
   $("#updated_end_time").val(updated_end_date);
   
   $('#updated_date_picker_start').datetimepicker({
   format:'YYYY-MM-DD HH:mm:ss',
   });
   $('#updated_date_picker_end').datetimepicker({
   format:'YYYY-MM-DD HH:mm:ss',
   useCurrent: false //Important! See issue #1075
   });
   $("#updated_date_picker_start").on("dp.change", function (e) {
   $('#updated_date_picker_end').data("DateTimePicker").minDate(e.date);
   });
   $("#updated_date_picker_end").on("dp.change", function (e) {
   $('#updated_date_picker_start').data("DateTimePicker").maxDate(e.date);
   });
});
</script>
