<?php

/*
|--------------------------------------------------------------------------
| Application Routes
|--------------------------------------------------------------------------
|
| Here is where you can register all of the routes for an application.
| It's a breeze. Simply tell <PERSON><PERSON> the URIs it should respond to
| and give it the Closure to execute when that URI is requested.
|
*/

//die('amgetting loaded');
//array('xssprotection','frameguard','admin')
Route::group(['module'=>'cms','namespace' => 'App\modules\backend\cms\Controllers','prefix'=>'admin','middleware'=>['web','admin','adminroleauthenticate']], function() {
		Route::get('/', 'C<PERSON><PERSON><PERSON>roller@getHomePage');
		Route::get('/home', 'C<PERSON><PERSON><PERSON>roller@getHomePage');
		Route::get('/dashboard', 'CmsController@getHomePage');

		Route::resource('admins', 'AdminsController');
		// Route::resource('jobroleprompts', 'jobrolepromptsController');
		Route::resource('jobroles', 'JobrolesController');
		Route::resource('uaquestions', 'UaquestionsController');
		Route::resource('uareports', 'UareportsController');
		Route::resource('uassessments', 'UassessmentsController');
		Route::resource('uprofiles', 'UprofilesController');
		Route::resource('users', 'UsersController');

		Route::get('/unauthorized', 'CmsController@unauthorized');
		Route::get('testemail', 'CmsController@testEmail');

		// Resume Reporting Panel
		Route::get('resumeusers', 'ResumesController@users');
		Route::get('resumelists', 'ResumesController@resumes');
		Route::get('resumeusers/export', 'ResumesController@exportUsersToCSV');
		
}); 
