<?php
namespace App\modules\frontend\cms\Controllers;
use App\Http\Models\Commonparser;
use App\User;
use App\UtmTracker;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Request as Directrequest;
use Socialite;
use Cookie;
use Session;
use GuzzleHttp\Exception\ClientException;
use Laravel\Socialite\Two\InvalidStateException;
use Illuminate\Support\Facades\Auth;

class SocialController extends Controller
{
    
    public function __construct()
    {
    }
    
   /* public function SocialSignup($provider)
    {
        die('social signup');
        $socialurl = Directrequest::getSchemeAndHttpHost();
	    $socialurl = str_replace('http://','https://',$socialurl);
        $oldsocial = config('services.'.$provider.'.redirect');
        if(strpos($oldsocial, 'https')===false)
        {
            config(['services.'.$provider.'.redirect' => $socialurl.'/'.$oldsocial]);
        }
        // echo $oldsocial; die;

        //set config in run time
         //set config in run time
         $getSocialCredential = getSocialCredential();

         if(!empty($getSocialCredential))
         {
            config(['services.'.$provider.'.client_id' => $getSocialCredential[$provider]['id']]);
            config(['services.'.$provider.'.client_secret' => $getSocialCredential[$provider]['secret']]);
         }

        // echo config('services.facebook.redirect'); die;
        $response = array();
        // Socialite will pick response data automatic 
        $userSocialInfo = Socialite::driver($provider)->stateless()->user();
        // p($user);
        $userData = $userSocialInfo->user;
        //$userData = array('id'=>1,'name'=>'raj','email'=>'<EMAIL>','source'=>'facebook');
        if(!empty($userData['id']) && !empty($userData['name']) && !empty($userData['email']))
        {
            $userData['source'] = $provider;
            $host = Directrequest::header('host');
            // p($host);
            $userData['host'] = $host;
        	$modules = \Config::get("mgl.social_login");
	        $parser = new Commonparser;
	        \Input::merge($userData);
	        $modules = $parser->parseRequest($modules,'signup');
            // p($modules);
	        $modulesResponse = array_merge(getCurlResponse($modules),$modules);
	        // p($modulesResponse);
            $userinfo = @$modulesResponse['signup'];
            // p($userinfo);
            if($userinfo['status_code']==200)
            {
                $security = $userinfo['response'][0]['security'];
                $user = $userinfo['response'][0]['users'];
                $usertype = $userinfo['response'][0]['usertype'];
                //for frontend purpose
                //user info
                //make($name, $value, $minutes = 0, $path = null, $domain = null, $secure = false, $httpOnly = true)
                //  Cookie::queue('cutk', json_encode($user), $security['access_token'],"","","",false);
                 //access token
                  Cookie::queue('ustk', $security['access_token'], $security['expires_in'],"","","",false);
                //  //refresh token
                //  Cookie::queue('rstk', $security['refresh_token'], $security['expires_in'],"","","",false);
                //  //expire of main token
                //  Cookie::queue('eustk', $security['expires_in'], $security['expires_in'],"","","",false);

                 //for server purpose
                 Session::put('cutk', json_encode($user), $security['access_token']);
                 //access token
                 Session::put('ustk', $security['access_token'], $security['expires_in']);
                 //refresh token
                 Session::put('rstk', $security['refresh_token'], $security['expires_in']);
                 //expire of main token
                 Session::put('eustk', $security['expires_in'], $security['expires_in']);

                 //set api key in session
                 Session::put('api_key', 'Bearer '.$security['access_token'], $security['expires_in']);

                //  Cookie::queue(Cookie::forget('cutka'));
                 session()->forget(['cutka','video_reward_contests','current_contest_id_reward_ad']);

            }
            // p($userinfo);

	        return response()->json(array('user'=>$user,'usertype'=>$usertype));
        }
        

        // return response()->json($modulesResponse);
    } */


    public function logout()
    {
        session()->forget(['cutk', 'ustk','rstk','eustk','api_key','ubal','playedGameId']);
        Session::flush();
        Cookie::queue(Cookie::forget('cutk'));
        Cookie::queue(Cookie::forget('ustk'));
        Cookie::queue(Cookie::forget('rstk'));
        Cookie::queue(Cookie::forget('eustk'));
        Cookie::queue(Cookie::forget('ubal'));
        Cookie::queue(Cookie::forget('playedGameId'));
        // p(Session::all());
        return response()->json(array('status'=>200,'message'=>'logged out successfully!'));

    }

    public function redirectToProvider($driver)
    {
        $provider = $driver;
        $socialurl = Directrequest::getSchemeAndHttpHost();
        $socialurl = str_replace('http://','https://',$socialurl);
            $oldsocial = config('services.'.$provider.'.redirect');
            if(strpos($oldsocial, 'https')===false)
            {
                config(['services.'.$provider.'.redirect' => $socialurl.'/'.$oldsocial]);
            }

        $config = config('services.google');
        //$config['redirect'] = config('app.url').'/'.$config['redirect']; 
        
            
            if($driver=="google"){
                $provider = Socialite::buildProvider(
                    \Laravel\Socialite\Two\GoogleProvider::class, 
                    $config
                );
            }
            return $provider->redirect();
       
    }

    public function handleProviderCallback( $provider )
    {
        $socialurl = Directrequest::getSchemeAndHttpHost();
	    $socialurl = str_replace('http://','https://',$socialurl);
        $oldsocial = config('services.'.$provider.'.redirect');
        if(strpos($oldsocial, 'https')===false)
        {
            config(['services.'.$provider.'.redirect' => $socialurl.'/'.$oldsocial]);
        }
        /*info('SOCIAL '.json_encode(config('services.google')));
        try {
                $user = Socialite::driver($provider)->user();
                var_dump($user);
         } catch (\GuzzleHttp\Exception\ClientException $e) {
             dd((string) $e->getResponse()->getBody());
        }die();
        */
        // Socialite will pick response data automatic 
        try {
            $userSocialInfo = Socialite::driver($provider)->stateless()->user();
            // var_dump($userSocialInfo);die;
            
            $userData = $userSocialInfo->user;
            $existingUser = User::where('email', $userData['email'])->first();
            $response = call_login_register($userData['email'],$userData['email'],$userData['name']);
            \Log::info('Api response ::'.@json_encode(@$response));
            if(isset($response['status']) && !empty($response['status'])){
                Session::put('cutk', $response['access_token'], $response['expiresIn']);
                Cookie::queue('cutk', $response['access_token'], $response['expiresIn'],"","","",false);

                // Check if user already exists
                
                $isNewUser = !$existingUser;

                $user = User::firstOrCreate(['email' => $userData['email']]);

                // Save UTM data only for new users
                if ($isNewUser && $user) {
                    $this->saveUtmDataForUser($user);
                }

                // Authenticate using the custom social guard
               \Auth::guard('social')->setUser($user);


                // Redirect to a protected route
                if($user){
                    Auth::login($user);
                    return redirect('lets-start');
                }
            }
            else{
                return redirect('lets-start');
            }
           
        } catch (ClientException $client_error) {
            \Log::info('LOGIN ERROR CLIENT ::'.@json_encode(@$client_error));
            return redirect()->route('home',['login_exception'=>'Something went wrong, please try logging again']);
        } catch (InvalidStateException $invalid_state) {
            \Log::info('LOGIN ERROR INVALID STATE ::'.@json_encode(@$invalid_state));
            return redirect()->route('home',['login_exception'=>'Something went wrong, please try logging again']);
        } catch (\ErrorException $ex) {
            \Log::info('LOGIN ERROR THIRD ::'.@json_encode(@$ex));
            return redirect()->route('home',['login_exception'=>'Something went wrong, please try logging again']);
        }
    }

    /**
     * Save UTM data for a new user and remove UTM cookies
     *
     * @param User $user
     * @return void
     */
    private function saveUtmDataForUser(User $user)
    {
        try {
            // Get UTM parameters from cookies
            $utmSource = Cookie::get('utm_source', 'Direct');
            $utmMedium = Cookie::get('utm_medium');
            $utmCampaign = Cookie::get('utm_campaign');

            // Create UTM tracker record
            UtmTracker::create([
                'user_id' => $user->id,
                'utm_source' => $utmSource,
                'utm_medium' => $utmMedium,
                'utm_campaign' => $utmCampaign,
            ]);

            // Remove UTM cookies after saving
            Cookie::queue(Cookie::forget('utm_source'));
            Cookie::queue(Cookie::forget('utm_medium'));
            Cookie::queue(Cookie::forget('utm_campaign'));

            \Log::info('UTM data saved for user: ' . $user->email . ' - Source: ' . $utmSource . ', Medium: ' . $utmMedium . ', Campaign: ' . $utmCampaign);

        } catch (\Exception $e) {
            \Log::error('Failed to save UTM data for user ' . $user->email . ': ' . $e->getMessage());
        }
    }
}
