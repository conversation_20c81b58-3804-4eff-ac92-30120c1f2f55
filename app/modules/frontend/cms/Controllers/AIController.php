<?php

namespace App\modules\frontend\cms\Controllers;

use App\Http\Requests;
use App\Http\Controllers\Controller;
use App\Http\Models\Commonparser;
use App\Http\Models\Game;
use App\Http\Models\Contest;
use App\Http\Models\User;
use App\Http\Models\Contestwinner;
use App\Http\Models\Userwallettranshistory;
use App\Http\Models\Userwalletcoin;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;
// use Request;
use Validator;
use Input;
use Redirect;
use DB;
use Auth;
use Cookie;
use Session;
use Illuminate\Support\Facades\Redis;
use Illuminate\Http\Request;

class AIController extends Controller
{

    /**14583
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function __construct() {}

    public function getHomePage()
    {
        return 'home page';
        //     $ext = pathinfo(\Request::getRequestUri(), PATHINFO_EXTENSION);
        //     if(in_array($ext, array('jpg','png','jpeg','woff2','css','js','map')))
        //     {
        //         exit;
        //     }


        //    // Cookie::queue('sociallogin', base64_encode(json_encode($getSocialCredential)), 604800,"","","",false);

        //     Session::put('sociallogin', base64_encode(json_encode($getSocialCredential)));


        //     setLog('request is coming!');
        //     $response = array();

        //     return view('cms::cms.home')->with($response);exit;
    }

    public function getBase()
    {
        return view('cms::landing1');
    }
    public function getSignin()
    {
        return view('cms::presignup.Signup');
    }


    public function letsStart()
    {
        $jobroles = Cache::remember('jobroles', 3600, function () {
            $accessToken = session()->get('cutk');
            $url = config("aireadiness.apis.getrole");
            return getWithBearerToken($url, $accessToken);
        });

        return view('cms::letsstart')->with("jobroles", $jobroles);
    }

    public function postLetsStart(Request $request)
    {

        $data =  $request->all();       
        // return $data;
        $url = \Config::get("aireadiness.apis.save_profile");
        $accessToken = session()->get('cutk');
        $reqdata = [
            'job_role_id' => $data['profile'],
            'current_role' => 'Full Stack Engineer',
            'exp_in_year' => $data['experience'],
            'primary_skill' => $data['primary-skill'],
            'secondary_skill' => $data['secondary-skill'],
            'is_ai_used'   => $data['ai-tools']
        ];
        session()->put('job_role_id', $data['profile']);
        session()->put('exp_in_year', $data['experience']); 
        session()->put('primary_skill', $data['primary-skill']);  
        session()->put('secondary_skill', $data['secondary-skill']);
        session()->put('is_ai_used', $data['ai-tools']);  
               
        $response = postWithBearerToken($url, $accessToken, $reqdata);
        $assessment_id = $response['data']['assessment_id'];
        session()->put('assessment_id', $assessment_id);       
        if ($assessment_id) {
            return [$assessment_id];
        } else {
            $this->response($response, 400, 'Profile api failed');
        }
    }

    public function stepForm()
    {

        
        $accessToken = session()->get('cutk');
        $assessment_id = session()->get('assessment_id');
        $url = \Config::get("aireadiness.apis.start_chat");
        $data['assessment_id'] = $assessment_id;
        $assessmentQuestion = postWithBearerToken($url, $accessToken, $data);        
        $response['data'] = [];
        session()->put('mobile_number', $assessmentQuestion['data']['mobile_number']);
        $response['question_messages'] = \Config::get("aireadiness.question_messages");
        $response['skip_messages'] = \Config::get("aireadiness.skip_messages");
        if (isset($assessmentQuestion['status']) && $assessmentQuestion['status'] != 200) {
            $response['data'] = $assessmentQuestion['data'];
            $response['error'] = $assessmentQuestion['data']['message'];
            $json_error = json_decode(@$assessmentQuestion['data']['raw_response']);
            $response['custom_error'] = (@$json_error->error) ? @$json_error->error : $assessmentQuestion['data']['message'];
            //p($response);
        } else {
            $response['data'] = $assessmentQuestion['data'];
        }

        //p($response);
        return view('cms::stepform')
            ->with("assessmentQuestion", $assessmentQuestion['data'])
            ->with("response", $response);
        exit;
    }

    public function postStepForm(Request $request)
    {
        $data =  $request->all();
        $level = 1;
        $job_role_id = session()->get('job_role_id');
        $only_mcq_job_role_id_list = [23,24,25,26];
        if(in_array($data['cq'],[3,7,12]) && !in_array($job_role_id,$only_mcq_job_role_id_list)){
            $url = \Config::get("aireadiness.apis.save_coding");
            if ($data['cq'] == 7) {
                $level = 2;
            }
            if ($data['cq'] == 12) {
                $level = 3;
            }
        } else {
            $url = \Config::get("aireadiness.apis.save_mcq");
        }
        $accessToken = session()->get('cutk');
        $assessment_id = session()->get('assessment_id');
        $reqdata = [
            'answer' => $data['answer'],
            'level' => $level,
            'question_id' => $data['question_id'],
            'assessment_id' => $assessment_id,
            'time_taken' => $data['time_taken']
        ];

        $response = postWithBearerToken($url, $accessToken, $reqdata);

        // p($response);
        if (isset($response['status']) && $response['status'] == 200) {
            return $this->response($response['data'], 200, 'next question detail');
        } else {
            return $this->response($response, 400, 'submit answer failed');
        }
    }

    public function getUnlockScore()
    {
        return view('cms::unlockscore');
        exit;
    }

    public function getOtpVerification()
    {

        return view('cms::otpverification');
        exit;
    }

    public function verifyOtp(Request $request)
    {
        if (!$request->ajax()) {
            return response()->json([
                'status' => 400,
                'data' => ['message' => 'Invalid Request']
            ]);
        }

        $validator = Validator::make($request->all(), [
            'enteredOTP' => ['required', 'digits:6', 'numeric'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 401,
                'data' => ['message' => 'Invalid OTP format. It must be a 6-digit number.']
            ]);
        }


        $enteredOTP = $request->input('enteredOTP');
        $mobile = session()->get('mob_no');

        $url = config('aireadiness.apis.verify_otp');
        $accessToken = session('cutk');
        $otpCounter = session()->get('otp_count');
        $startOtpTime = session()->get('otp_time');
        $currentTime = now();



        if ($otpCounter > 3) {
            if ($startOtpTime->diffInMinutes($currentTime) < 15) {
                return response()->json([
                    'status' => 401,
                    'data' => ['message' => 'Too many attempts. Please wait 15 minutes.']
                ]);
            } else {
                session()->put('otp_count', 1);
                session()->put('otp_time', now());
            }
        }



        if (!$accessToken) {
            return response()->json([
                'status' => 401,
                'data' => ['message' => 'Unauthorized: Missing access token']
            ]);
        }

        $reqData = [
            'mobile' => '91' . $mobile,
            'otp'    => $enteredOTP,
        ];

        try {
            $response = postWithBearerToken($url, $accessToken, $reqData);
            $otpCount = session()->get('otp_count');
            $otpCount += 1;
            session()->put('otp_count', $otpCount);


            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'data' => ['message' => 'Server Error', 'error' => $e->getMessage()]
            ]);
        }
    }



    public function otpResend(Request $request)
    {
        if (!$request->ajax()) {
            return response()->json([
                'status' => 400,
                'data' => ['message' => 'Invalid Request']
            ]);
        }

        $otpCounter = session()->get('otp_count');
        $startOtpTime = session()->get('otp_time');
        $currentTime = now();



        if ($otpCounter > 3) {
            if ($startOtpTime->diffInMinutes($currentTime) < 15) {
                return response()->json([
                    'status' => 401,
                    'data' => ['message' => 'Too many attempts. Please wait 15 minutes.']
                ]);
            } else {
                session()->put('otp_count', 1);
                session()->put('otp_time', now());
            }
        }




        $url = config('aireadiness.apis.send_otp');
        $accessToken = session('cutk');

        if (!$accessToken) {
            return response()->json([
                'status' => 401,
                'data' => ['message' => 'Unauthorized: Missing access token']
            ]);
        }
        $mob = session()->get('mob_no');

        $reqData = [
            'mobile' => '91' . $mob,

        ];

        try {
            $response = postWithBearerToken($url, $accessToken, $reqData);
            $otpCount = session()->get('otp_count');
            $otpCount += 1;
            session()->put('otp_count', $otpCount);

            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'data' => ['message' => 'Server Error', 'error' => $e->getMessage()]
            ]);
        }
    }


    public function postSubmitMob(Request $request)
    {

        $validated = $request->validate([
            'mobNumber' => ['required', 'regex:/^[6-9]\d{9}$/'],
        ]);

        $mob = $request->input('mobNumber');


        $url = \Config::get("aireadiness.apis.send_otp");
        $accessToken = session()->get('cutk');
        $reqdata = [
            'mobile' => '91' . $mob,

        ];
        $response = postWithBearerToken($url, $accessToken, $reqdata);
        // p($response);

        $statusList = [500, 400, 404];

        $status = isset($response['status']) ? $response['status'] : '';
        $message = isset($response['data']['message']) ? $response['data']['message'] : '';

        if ($status == 200) {
            session()->put('otp_time', now());
            session()->put('otp_count', 1);
            session()->put('mob_no', $mob);
            $response = [
                'message' => 'OTP successfully sent to your mobile number.',
                'mob_no' => $mob,
            ];
        } elseif ($message == "Mobile number already exists, OTP not needed") {
            $id = base64_encode(session()->get('assessment_id'));
            return redirect('/assessment-report/' . $id);
        } elseif (in_array($status, $statusList)) {
            return redirect()->back()->withErrors(['error' => isset($response['data']['error']) ? $response['data']['error'] : '']);
        } else {
            $response = [
                'message' => 'Something went wrong',
                'mob_no' => $mob,

            ];
            return view('cms::unlockscore');
            exit;
        }
        return view('cms::otpverification', $response);
    }


    public function assessmentReport($id)
    {
        try {
            // Decode assessment ID with strict mode
            $assessment_id = base64_decode($id, true);

            if ($assessment_id === false) {
                return redirect('/lets-start')->withErrors('Invalid assessment ID.');
            }

            // Check for access token
            $accessToken = session()->get('cutk');
            if (!$accessToken) {
                return redirect('/signup')->withErrors('Session expired. Please login again.');
            }

            $url = \Config::get("aireadiness.apis.assessment_report");
            $data = ['assessment_id' => $assessment_id];

            // Call external API
            $assessmentReportDetail = postWithBearerToken($url, $accessToken, $data);

            // Redirect if current session user matches the report user
            if (
                isset($assessmentReportDetail['data']['context']['user']['id']) &&
                session()->has('social_user_id') &&
                session()->get('social_user_id') == $assessmentReportDetail['data']['context']['user']['id']
            ) {
                try {
                    $feedback = ($assessmentReportDetail['data']['context']['feedback']) ? "yes" : null;
                } catch (Exception $e) {
                    $feedback = null;
                }
                return view('cms::assessment_report')
                ->with("assessmentReportDetail", $assessmentReportDetail['data'])
                ->with("assessment_id", $assessment_id)
                ->with("feedback",$feedback);
            }
            else{
                return redirect('/lets-start')->withErrors('Invalid assessment ID.');
            }        

        } catch (\Throwable $e) {
            \Log::error('Error in assessmentReport(): ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'id' => $id,
                'decoded_id' => $assessment_id ?? null,
            ]);

            return redirect('/lets-start')->withErrors('Invalid assessment ID.');
        }
    }

    public function getPublicReport($id)
    {
        try {
            $assessment_id = base64_decode($id, true);

            if ($assessment_id === false) {
                return redirect('/lets-start')->withErrors('Invalid assessment ID.');
            }

            $url = \Config::get("aireadiness.apis.public_report");

            $data = [
                'assessment_id' => $assessment_id
            ];
            $accessToken = session()->get('cutk');
            $is_loggedin = true;
            if (!$accessToken) {
                $is_loggedin = false;
            }
            $assessmentReportDetail = postWithBearerToken($url, $accessToken, $data);
        
            if (
                isset($assessmentReportDetail['data']['context']['user']['id']) &&
                session()->has('social_user_id') &&
                session()->get('social_user_id') == $assessmentReportDetail['data']['context']['user']['id']
            ) {
                return redirect()->to(url('assessment-report/' . $id));
            }

            return view('cms::public_report')->with("assessmentReportDetail", $assessmentReportDetail['data'])->with("isLoggedIn",$is_loggedin);
        } catch (\Exception $e) {
            \Log::error('Error in getPublicReport: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return redirect('/lets-start')->withErrors('Something went wrong while loading the report.');
        }
    }


    public function pageNotFound()
    {
        $response = array();
        return 'page not found';
        return view('cms::common.PageNotFound')->with($response);
        exit;
    }

    public function clearSessionKey($key)
    {
        if (Session::has($key)) {
            Session::forget($key);
        }
    }

    public function logout()
    {
        Auth::logout();
        session()->forget(['cutka', 'ubal', 'playedGameId', 'uvisit', 'mobile_number']);
        Cookie::queue(Cookie::forget('uvisit'));
        Cookie::queue(Cookie::forget('cutka'));
        Cookie::queue(Cookie::forget('cutk'));
        Cookie::queue(Cookie::forget('ubal'));
        Cookie::queue(Cookie::forget('playedGameId'));
        Session::flush();
        return redirect('/');
    }
    public function postRatingAndFeedback(Request $request)
    {
        try {
            $validated = $request->validate([
                'rating' => 'required|integer|min:1|max:5',
                'feedback' => 'nullable|string|max:1000',
            ]);

            $accessToken = session()->get('cutk');
            $assessment_id = session()->get('assessment_id');

            if (!$accessToken || !$assessment_id) {
                return response()->json([
                    'status' => 'error',
                    'status_code' => 401,
                    'message' => 'Session expired or missing required session data.'
                ], 401);
            }

            $reqdata = [
                'assessment_id' => $assessment_id,
                'rating'        => $validated['rating'],
                'feedback'      => $validated['feedback'] ?? null,
            ];

            $url = config("aireadiness.apis.submit_feedback");
            $response = postWithBearerToken($url, $accessToken, $reqdata);

            if (isset($response['status']) && in_array($response['status'], [200, 201])) {
                return response()->json([
                    'status'      => 'success',
                    'status_code' => 200,
                    'message'     => 'Feedback submitted successfully.',
                    'response'    => $response
                ], 200);
            } else {
                return response()->json([
                    'status'      => 'error',
                    'status_code' => $response['status'] ?? 400,
                    'message'     => $response['message'] ?? 'Something went wrong while submitting feedback.',
                    'response'    => $response
                ], 400);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            // validation fail
            return response()->json([
                'status'      => 'error',
                'status_code' => 422,
                'message'     => 'Validation failed.',
                'errors'      => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            \Log::error('Feedback submission error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'status'      => 'error',
                'status_code' => 500,
                'message'     => 'An unexpected error occurred. Please try again later.'
            ], 500);
        }
    }

}
