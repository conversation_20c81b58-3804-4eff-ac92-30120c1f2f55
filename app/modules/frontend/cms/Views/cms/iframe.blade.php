
<script src="https://code.jquery.com/jquery-2.2.4.min.js" integrity="sha256-BbhdlvQf/xTY9gja0Dq3HiwQF8LaCRTXxZKRutelT44=" crossorigin="anonymous"></script>
<?php /*<script type="text/javascript">

	window.onGameStart = function () {
   console.log('calling my function')
   console.log("Game Started");
}
gameEndEvent = function (score) {
   console.log('calling my function'+score)
}

window.gamePauseEvent = function () {
   console.log('calling my function')
}
window.gameUnPauseEvent = function () {
   console.log('calling my function')
}
window.gameQuitEvent = function (score) {
   console.log('calling my function'+score)
}
window.gameRestartEvent = function () {
   console.log('calling my function')
}
window.gameQuitEvent2 = function (score) {
   console.log('calling my function'+score)
}


</script>

<iframe id="myIFrame" src="https://radxsoft.com/sites/games/"></iframe>

*/
?>
<style type="text/css">
	div {
  margin-top: 3px;
  padding: 0 10px;
}

button {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  cursor: pointer;
  font-weight: 700;
  font-size: 13px;
  padding: 8px 18px 10px;
  line-height: 1;
  color: #fff;
  background: #345;
  border: 0;
  border-radius: 4px;
  margin-left: 0.75em;
}

p {
  display: inline-block;
  margin-left: 10px;
}

</style>
<script type="text/javascript">
	/*$(function() {
    var player = $('iframe');
    var playerOrigin = '*';
    var status = $('.status');

    // Listen for messages from the player
    if (window.addEventListener) {
        window.addEventListener('message', onMessageReceived, false);
    }
    else {
        window.attachEvent('onmessage', onMessageReceived, false);
    }

    window.addEventListener('onGameStart',onGameStart,false);

    function onGameStart()
    {
    	console.log('game start detected');
    }

     window.onGameStart()
    {
    	console.log('game start detected');
    }

    // Handle messages received from the player
    function onMessageReceived(event) {

    	console.log(event);
        // Handle messages from the vimeo player only
        if (!(/^https?:\/\/radxsoft.com/).test(event.origin)) {
            return false;
        }
        
        if (playerOrigin === '*') {
            playerOrigin = event.origin;
        }
        
        var data = JSON.parse(event.data);
        console.log(data);
        console.log(data.event);
        
        switch (data.event) {
            case 'ready':
                onReady();
                break;
               
            case 'playProgress':
                onPlayProgress(data.data);
                break;
                
            case 'pause':
                onPause();
                break;
               
            case 'finish':
                onFinish();
                break;
        }
    }

    // Call the API when a button is pressed
    $('button').on('click', function() {
        post($(this).text().toLowerCase());
    });

    // Helper function for sending a message to the player
    function post(action, value) {
        var data = {
          method: action
        };
        
        if (value) {
            data.value = value;
        }
        
        var message = JSON.stringify(data);
        player[0].contentWindow.postMessage(message, playerOrigin);
    }

    function onReady() {
        status.text('ready');
        
        post('addEventListener', 'pause');
        post('addEventListener', 'finish');
        post('addEventListener', 'playProgress');
        post('addEventListener', 'gameStartEvent');
    }

    function onPause() {
        status.text('paused');
    }

    function onFinish() {
        status.text('finished');
    }

    function onPlayProgress(data) {
        status.text(data.seconds + 's played');
    }
    function onPlaygameStartEvent(data) {
        status.text('game started');
    }
    function ongameEndEvent(data) {
        status.text('game started'+data);
    }
    
});
*/
if (window.addEventListener) {
   window.addEventListener("message", onMessage, false);        
} 
else if (window.attachEvent) {
    window.attachEvent("onmessage", onMessage, false);
}

function onMessage(event) {
    // Check sender origin to be trusted
    //if (event.origin !== "http://example.com") return;
    console.log(event);
    var data = event.data;

    if (typeof(window[data.func]) == "function") {
        window[data.func].call(null, data.message);
    }
}

// Function to be called from iframe
function onGameStart(message) {
    console.log(message);
}
function onGameEnd(score) {
    console.log('score received from server:'+score);
}

</script>
<div>
This is a fork of a test made by https://codepen.io/bdougherty/
</div>
<!-- <iframe id="player1" src="https://player.vimeo.com/video/76979871?api=1&player_id=player1" width="630" height="354" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe> -->
<iframe id="player1" src="https://radxsoft.com/sites/games/" width="630" height="354" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>

<div>
  <button>Play</button>
  <button>Pause</button>
  <p>Status: <span class="status">&hellip;</span></p>
</div>

