@extends('cms::layout')

@section('title', 'TechGig | Onboarding')
@section('content')

    <!-- Main Form -->
    <main role="main">
        <div class="otp-box common-container">
            <img src="{{ getImgUrl('/img/one-time-password.png') }}" alt="Phone OTP Icon" loading="lazy" />
            <h2>OTP Verification</h2>

            <p>One Time Password (OTP) has been sent to
                <span class="mobile-number">
                    @if (isset($mob_no))
                        {{ $mob_no }}
                    @endif
                    <a href="/readiness/unlock-your-score" class="edit-icon" aria-label="Edit mobile number">
                        <img src="{{ getImgUrl('/img/edit.png') }}" alt="Edit mobile number" loading="lazy">
                    </a>

                </span>
            </p>

            <!-- OTP Form -->
            <form onsubmit="return validateOTP(event)">
                <div class="form-container">
                    <input type="text" id="otp" name="otp" placeholder="Enter your OTP" maxlength="6"
                        aria-describedby="otpError" required inputmode="numeric" />
                    <div class="validation-box"></div>
                    <div class="otp-error-msg">
                        <img src="{{ getImgUrl('/img/cross.png') }}" alt="Error icon" class="error-icon" />
                        <span>Invalid OTP</span>
                    </div>
                </div>

                <button type="submit">SUBMIT</button>
            </form>

            <!-- Timer -->
            <div class="resend">
                Resend OTP in : <span class="timer">00:30</span>
            </div>

            <!-- Resend Link -->
            <div class="resend-container">
                <a href="#" id="resendBtn">
                    Resend OTP
                </a>
            </div>
        </div>
    </main>

@endsection

@section('onpagejs')

    <script>
        var AssessId = "<?php echo base64_encode(session()->get('assessment_id')); ?>";

        let timerDisplay = document.querySelector('.timer');
        let resendBox = document.querySelector('.resend');
        let resendContainer = document.querySelector('.resend-container');
        const otpErrorMsg = document.querySelector(".otp-error-msg")
        const validationMsg = document.querySelector(".validation-box")
        const otpInput = document.getElementById("otp");


        let countdown;
        let timerInterval;

        function startTimer() {
            clearInterval(timerInterval); // stop any previous timer

            countdown = 30; // reset time
            resendBox.style.display = "block";
            resendContainer.style.display = "none";

            timerInterval = setInterval(() => {
                countdown--;
                let seconds = countdown < 10 ? '0' + countdown : countdown;
                timerDisplay.textContent = `00:${seconds}`;

                if (countdown <= 0) {
                    clearInterval(timerInterval);
                    resendBox.style.display = "none";
                    resendContainer.style.display = "flex";
                    otpErrorMsg.classList.remove("show");
                }
            }, 1000);
        }


        // Start on page load
        startTimer();


        function validateOTP(event) {
            event.preventDefault();

            const enteredOTP = document.getElementById("otp").value;
            const errorBox = document.querySelector(".otp-error-msg");

            $.ajax({
                url: '{{ url('/otp-verify') }}',
                type: 'POST',
                data: {
                    enteredOTP: enteredOTP,
                    _token: '{{ csrf_token() }}' // Important for Laravel CSRF protection
                },
                success: function(response) {
                    if (response.status === 200) {
                        validationMsg.classList.add("show");
                        validationMsg.textContent = response.data.message;
                        window.location.href = base_url + '/assessment-report/' + AssessId
                    } else {
                        otpErrorMsg.classList.add("show");
                        validationMsg.classList.remove("show");
                    }
                },
                error: function(xhr) {
                    console.error("Error:", xhr.responseText);
                }
            });

            return false;
        }

        $('#resendBtn').on('click', function(e) {
            e.preventDefault();

            $.ajax({
                url: '{{ url('/otp-resend') }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    validationMsg.classList.add("show");
                    validationMsg.textContent = response.data.message;

                    // Reset OTP input and error messages
                    otpErrorMsg.classList.remove("show");
                    otpInput.value = "";

                    // Restart timer
                    startTimer();

                    // Hide message after 3s
                    setTimeout(() => {
                        validationMsg.classList.remove("show");
                    }, 3000);
                },
                error: function(xhr) {
                    console.error("Error:", xhr.responseText);
                }
            });
        });
    </script>

@endsection
