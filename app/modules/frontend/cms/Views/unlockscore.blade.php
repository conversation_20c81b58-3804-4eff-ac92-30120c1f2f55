@extends('cms::layout')

@section('title', 'TechGig | Onboarding')
@section('content')
    
            <!-- Main Form -->
            <main role="main">                           
                <div class="unlock-box common-container">
                  
                    <img src="{{getImgUrl('/img/unlock_icon.png')}}" alt="Illustration of locked score meter" loading="lazy" />
                    <div class="unlock-label">UNLOCK YOUR SCORE</div>

                    <h3>You're one step away!</h3>
                    <p>Submit your mobile number to get your assessment</p>

                    <form aria-label="Mobile number submission form" method="POST" action="{{ route('postSubmitMob') }}">
                        @csrf
                        <input type="tel" name="mobNumber" id="mobNumber" placeholder="Enter your mobile number"
                            pattern="[0-9]{10}" maxlength="10" required inputmode="numeric" aria-required="true" />

                        @if ($errors->any())
                            <div class="alert-alert-danger" id="error-alert">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>

                            <script>
                                // Hide the alert after 3 seconds (3000 milliseconds)
                                setTimeout(function() {
                                    const alert = document.getElementById('error-alert');
                                    if (alert) {
                                        alert.style.transition = 'opacity 0.5s ease';
                                        alert.style.opacity = '0';
                                        setTimeout(() => alert.style.display = 'none', 500); // fully hide after fade out
                                    }
                                }, 3000);
                            </script>
                        @endif




                        <button type="submit" aria-label="Submit mobile number">SUBMIT</button>
                    </form>
                    
                </div>
            </main>
            
@endsection

@section('onpagejs')
 
@endsection
