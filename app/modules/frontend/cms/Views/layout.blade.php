<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@yield('title', 'TechGIG')</title>
    <meta name="author" content="TechGIG" />
    <!-- Open Graph meta tags -->
    <meta property="og:title" content="@yield('og_title', 'TechGIG')" />
    <meta property="og:description" content="@yield('og_description', 'Your gateway to tech assessments and insights.')" />
    <meta property="og:url" content="@yield('og_url', url()->current())" />
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="TechGig" />
    <meta property="og:image" content="{{ imgUrl('/img/TechGigLogo.png') }}" />


    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Mona+Sans:wght@400;600;700&display=swap" rel="stylesheet" />

    <link rel="stylesheet" href="{{ getImgUrl('/fonts/font.css') }}">


    <!-- External CSS -->
    <link rel="stylesheet" href="{{ getImgUrl('/css/style.css') }}" />
    <link rel="stylesheet" href="{{ getImgUrl('/css/responsive.css') }}">

    <!-- Favicon (optional) -->
    <link rel="icon" href="{{ getImgUrl('/img/favicon.png') }}" type="image/x-icon" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script>
        var base_url = "<?php echo URL::to('/'); ?>";
    </script>
    <?php
    if(env('APP_ENV') == 'production') { ?>



    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-XHTN8DMGD4"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-XHTN8DMGD4');
    </script>


    <script>
        ! function(f, b, e, v, n, t, s) {
            if (f.fbq) return;
            n = f.fbq = function() {
                n.callMethod ?
                    n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n;
            n.push = n;
            n.loaded = !0;
            n.version = '2.0';
            n.queue = [];
            t = b.createElement(e);
            t.async = !0;
            t.src = v;
            s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window, document, 'script',
            'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '778180041550667');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
            src="https://www.facebook.com/tr?id=778180041550667&ev=PageView&noscript=1" /></noscript>
    <script type="text/javascript">
        (function(c, l, a, r, i, t, y) {
            c[a] = c[a] || function() {
                (c[a].q = c[a].q || []).push(arguments)
            };
            t = l.createElement(r);
            t.async = 1;
            t.src = "https://www.clarity.ms/tag/" + i;
            y = l.getElementsByTagName(r)[0];
            y.parentNode.insertBefore(t, y);
        })(window, document, "clarity", "script", "sit8na0ak8");
    </script>

    <?php }?>

</head>

<body>
    <section class="wrapper login-wrapper">
        <div class="wrp_container">

            <!-- Header -->
            <header>
                <a href="<?php echo URL::to('/'); ?>" class="tg_logo" aria-label="TechGIG Home">
                    <img src="{{ getImgUrl('/img/TechGigLogo.png') }}" alt="TechGIG logo" loading="lazy" />
                </a>
            </header>

            @yield('content')

            <main role="main" class="loader-wrap">
                <div class="completion-box common-container" aria-labelledby="levelCompleteHeading">
                    <div class="questions-loader">
                        <div class="loader"></div>
                    </div>
                    <p id="loader-msg">
                        You're among the <strong class="highlight">top 75%</strong> who cleared Level 1 — <span></span>
                        great start! Now’s your chance to stand out. <span></span>
                        Level 2 awaits—fewer make it, will you?
                    </p>
                </div>
            </main>
        </div>
        <div class="loader-container">
            <div class="loader"></div>
        </div>
    </section>
    <script>
        var currentPath = window.location.pathname;

        // If not home page
        if (currentPath !== '/signup') {
            var section = document.querySelector('section.wrapper');
            if (section) {
                section.classList.remove('login-wrapper');
            }
        }
        if (currentPath.includes('/lets-start/step-1')) {
            var section = document.querySelector('section.wrapper');
            if (section) {
                section.classList.add('question-screen-container');
            }
        }
    </script>
    @yield('onpagejs')
</body>

</html>
