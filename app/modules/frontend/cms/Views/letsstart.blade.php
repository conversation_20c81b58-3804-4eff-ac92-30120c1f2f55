@extends('cms::layout')

@section('title', 'TechGig | Onboarding')
@section('content')
    
            <!-- Main Form -->
            <main role="main">
                <div class="form-wrapper">
                     <h2>Let's Begin</h2>
                    <p class="subheading">Power up your progress. <span></span> The AI future starts here.</p>
                    <form id="assessmentForm">
                        <div class="form-grid">
                            <!-- Profile -->
                            <div class="form-group">
                                <label for="profile">Please select your profile</label>
                                <select id="profile" name="profile" required>
                                <option selected disabled>Select</option>
                                @foreach($jobroles as $key => $value)
                                    <option value="{{ $value['id'] }}">{{ $value['role_name'] }}</option>
                                @endforeach
                                </select>
                                <p class="err-msg">Please select your profile.</p>
                            </div>

                            <!-- Primary Skill -->
                            <div class="form-group">
                                <label for="primary-skill">Please select your primary skill</label>
                                <input type="text" id="primary-skill" name="primary-skill" placeholder="Enter"
                                    required />
                                <p class="err-msg">Please enter your primary skill.</p>
                            </div>

                            <!-- Experience -->
                            <div class="form-group">
                                <label for="experience">Years of Experience</label>
                                <select id="experience" name="experience" required>
                                    <option selected disabled>Select</option>
                                    <option value="1">0-1</option>
                                    <option value="3">1-3</option>
                                    <option value="5">3-7</option>
                                    <option value="6">7+</option>
                                </select>
                                <p class="err-msg">Please select your years of experience.</p>
                            </div>

                            <!-- Secondary Skill -->
                            <div class="form-group form-group-second">
                                <label for="secondary-skill">Please select your secondary skill</label>
                                <input type="text" id="secondary-skill" name="secondary-skill" placeholder="Enter" />
                            </div>
                        </div>

                        <!-- AI Tools Radio -->
                        <fieldset class="form-group form-group-second form-radio-grp">
                            <legend>Have you used any AI Tools before?</legend>
                            <div class="radio-group">
                                <label class="radio-option">
                                    <input type="radio" name="ai-tools" value="Yes"/>
                                    <span>Yes</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="ai-tools" value="No" checked/>
                                    <span>No</span>
                                </label>
                            </div>
                        </fieldset>

                        <!-- Footer: Icons + Submit -->
                        <div class="form-footer">
                            <div class="footer-icons" aria-hidden="true">
                                <div>
                                    <img src="{{getImgUrl('/img/1-icon.png')}}" alt="Rocket Icon" loading="lazy" width="32"
                                        height="32" />
                                </div>
                                <div>
                                    <img src="{{getImgUrl('/img/emoji.png')}}" alt="Emoji Icon" loading="lazy" width="32" height="32" />
                                </div>
                                <div>
                                    <img src="{{getImgUrl('/img/ChatGPT_logo.png')}}" alt="ChatGPT Logo" loading="lazy" width="32"
                                        height="32" />
                                </div>
                            </div>
                            <button type="button" class="submit-btn" onclick="javascript:void(0); startAssessment();">START THE ASSESSMENT</button>
                        </div>
                    </form>
                </div>
            </main>
            
@endsection

@section('onpagejs')
    <script>
        function startAssessment(){
            const form = document.getElementById('assessmentForm');
            const formData = new FormData(form);

            // Convert to a plain object (optional)
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });
            let errorFound = 0;
            if (!$("#profile").val()) {
                $("#profile").next(".err-msg").addClass("show");
                errorFound = 1;
            } else {
                $("#profile").next(".err-msg").removeClass("show");
            }
            if (!$("#primary-skill").val()) {
                errorFound = 1;
                $("#primary-skill").next(".err-msg").addClass("show");
            } else {
                $("#primary-skill").next(".err-msg").removeClass("show");
            }
            if (!$("#experience").val()) {
                errorFound = 1;
                $("#experience").next(".err-msg").addClass("show");
            } else {
                $("#experience").next(".err-msg").removeClass("show");
            }
            if(errorFound == 1){
                return false;
            }
             //console.log(data,formData);
             //return false;
            $(".loader-container").addClass("active");
            const url = base_url+'/lets-start';
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': "{{csrf_token()}}",
                },
                body: JSON.stringify(data)
            })
            .then(data => {
                window.location.href = base_url+'/lets-start/step-1'
            })
            .catch(error => {
                $(".loader-container").removeClass("active");
                console.error('Error:', error);
            });
        }
    </script>
@endsection
