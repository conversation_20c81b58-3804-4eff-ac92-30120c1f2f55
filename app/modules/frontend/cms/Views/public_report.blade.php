@extends('cms::layout')
@php
        $assessment_id = request()->segment(count(request()->segments()));    
        $publicURL = URL::to('/public-report').'/'.$assessment_id;
        $score = @$assessmentReportDetail['report']['total_score'] ?? 0;
        $og_title = 'I just took my AI Readiness Assessment and scored '.$score.'/100.';
@endphp

@section('title', 'TechGig | Assessment Report')
@section('og_title', $og_title)
@section('og_description', 'Take your FREE assessment here :')
@section('og_url', $publicURL)

@section('content')
    <style>
        .wrapper header {
            position: relative;
            display: flex;
            top: unset;
            left: unset;
            transform: unset;
        }

        .wrapper main{
            display: block;
        }
    </style>

    <!-- Main Content -->
    <main role="main" class="assessment-content">
        <div class="report-header">
            <div class="left">
                <h2>Assessment Report</h2>
            </div>
        </div>
        <div class="report-summary-card">
            <div class="profile-details">
                <div>
                    <h4>Job Role</h4>
                    <h5>
                        {{ $assessmentReportDetail['context']['jobrole']['role_name'] ?? '' }}
                    </h5>
                </div>
                <div>
                    <h4>Primary Skill</h4>
                    <h5>{{ $assessmentReportDetail['context']['assessment']['primary_skill'] ?? '' }}</h5>
                </div>
                <div>
                    <h4>Secondary Skill</h4>
                    <h5>{{ $assessmentReportDetail['context']['assessment']['secondary_skill'] ?? '' }}</h5>
                </div>
                <div>
                    <h4>Years of Experience</h4>
                    <h5>
                        @php
                            $expInYear = $assessmentReportDetail['context']['assessment']['exp_in_year'] ?? null;
                        @endphp

                        @if ($expInYear == 1)
                            0-1 Year
                        @elseif ($expInYear == 3)
                            3-5 Years
                        @elseif ($expInYear == 5)
                            5-7 Years
                        @elseif ($expInYear == 6)
                            7+ Years
                        @else
                            Unknown
                        @endif
                    </h5>
                </div>
            </div>
            <div class="report-details public-report-details">
                <div>
                    <img src="{{ getImgUrl('/img/report-bg-mobile.png') }}" loading="lazy" alt="Report">
                    @php
                        $score = @$assessmentReportDetail['report']['total_score'] ?? 0;
                        $scoreClass = '';

                        if ($score >= 0 && $score <= 39) {
                            $scoreClass = 'text-beginner';
                        } elseif ($score >= 40 && $score <= 59) {
                            $scoreClass = 'text-pioneer';
                        } elseif ($score >= 60 && $score <= 79) {
                            $scoreClass = 'text-curious';
                        } elseif ($score >= 80 && $score <= 100) {
                            $scoreClass = 'text-ready';
                        }
                    @endphp

                    <h2 class="{{ $scoreClass }}">{{ $score }}</h2>
                    </h2>
                </div>
                <h3>{{$assessmentReportDetail['context']['user']['full_name'] ?? ""}} scored
                    <strong><span>{{ @$assessmentReportDetail['report']['total_score'] ? @$assessmentReportDetail['report']['total_score'] : '0' }}/100</span>
                        points</strong>
                </h3>
                @php
                    $tier = @$assessmentReportDetail['report']['readiness_tier'];
                    $score = @$assessmentReportDetail['report']['total_score'] ?? 0;
                    $scoreClass = '';

                    if ($score >= 0 && $score <= 39) {
                        $scoreClass = 'text-beginner';
                    } elseif ($score >= 40 && $score <= 59) {
                        $scoreClass = 'text-pioneer';
                    } elseif ($score >= 60 && $score <= 79) {
                        $scoreClass = 'text-curious';
                    } elseif ($score >= 80 && $score <= 100) {
                        $scoreClass = 'text-ready';
                    }
                @endphp

                <h4 class="{{ $scoreClass }}">
                {{$assessmentReportDetail['context']['user']['full_name'] ?? ""}} is an {{ $tier ?? '' }}
                </h4>
            </div>
            <div class="report-tabs-container public-login-box">
               <h6>Discover your <span class="line-br"></span>  AI Readiness Score,<span class="line-br"></span>
                <span>just like {{$assessmentReportDetail['context']['user']['full_name'] ?? ""}}!</span>
               </h6>
               @if(Auth::check())
                <a href="<?php echo URL::to('/lets-start'); ?>" class="public-login-btn login_btn google" aria-label="Continue with Google">
                        <img src="{{imgUrl('/img/Google__icon.png')}}" alt="Google icon" loading="lazy">
                        Continue with Google
                </a>
               @else
                <a href="<?php echo URL::to('/signup'); ?>" class="public-login-btn login_btn google" aria-label="Continue with Google">
                    <img src="{{imgUrl('/img/Google__icon.png')}}" alt="Google icon" loading="lazy">
                    Continue with Google
                </a>
               @endif
               
               <p>By continuing, you agree with TechGIG <span class="line-br"></span> <a href="https://www.techgig.com/user/privacy" target="_blank" rel="nofollow">Privacy Policy</a> and <a href="https://www.techgig.com/user/terms" target="_blank" rel="nofollow">Terms of Use</a></p>

            </div>
        </div>

    </main>

@endsection
