@extends('cms::layout')

@section('title', 'TechGig | Assessment')
@section('content')
    <main role="main" class="main-question-screen {{ @$response['custom_error'] ? 'hide' : '' }}">
        <div class="question-box common-container">
            <div class="top-header-content">

                <!-- Question Header -->
                <div class="question-header">
                    <h3>Question <span id="current_question"><?php echo @$assessmentQuestion['current_question']; ?></span>/<span
                            id="total_question"><?php echo @$assessmentQuestion['total_question']; ?></span>:</h3>
                </div>
                <div class="levels-sec">
                    <div class="alarm-sec">
                        <img src="{{ getImgUrl('/img/alarm.png') }}" alt="alarm">
                        <div id="stopwatchTimer">
                            00:00
                        </div>
                    </div>

                </div>
            </div>

            <form id="questionForm">

                <!-- Question -->
                <div class="question-title" id="question_title">
                    {{ @$assessmentQuestion['question']['question'] ? @$assessmentQuestion['question']['question'] : '' }}
                </div>
                <input type="hidden" id="question_id" name="question_id"
                    value="{{ @$assessmentQuestion['question_id'] ? @$assessmentQuestion['question_id'] : '' }}" />
                <input type="hidden" id="question_type" name="question_type"
                    value="{{ @$assessmentQuestion['question']['question_type'] ? @$assessmentQuestion['question']['question_type'] : '' }}" />
                <input type="hidden" id="cq" name="cq"
                    value="{{ @$assessmentQuestion['question']['current_question'] ? @$assessmentQuestion['question']['current_question'] : 1 }}" />
                <section id="mcq_wrap">
                    <div class="instruction">Please select an option</div>

                    <!-- Options -->
                    <div class="options">
                        <label class="option">
                            <input type="radio" name="answer" value="A" />
                            <div class="option-label">A</div>
                            <div id="div_opt_a">
                                {{ @$assessmentQuestion['question']['option_A'] ? $assessmentQuestion['question']['option_A'] : '' }}
                            </div>
                        </label>

                        <label class="option">
                            <input type="radio" name="answer" value="B" />
                            <div class="option-label">B</div>
                            <div id="div_opt_b">
                                {{ @$assessmentQuestion['question']['option_B'] ? $assessmentQuestion['question']['option_B'] : '' }}
                            </div>
                        </label>

                        <label class="option">
                            <input type="radio" name="answer" value="C" />
                            <div class="option-label">C</div>
                            <div id="div_opt_c">
                                {{ @$assessmentQuestion['question']['option_C'] ? $assessmentQuestion['question']['option_C'] : '' }}
                            </div>
                        </label>

                        <label class="option">
                            <input type="radio" name="answer" value="D" />
                            <div class="option-label">D</div>
                            <div id="div_opt_d">
                                {{ @$assessmentQuestion['question']['option_D'] ? $assessmentQuestion['question']['option_D'] : '' }}
                            </div>
                        </label>
                    </div>
                </section>

                <section id="coding_wrap">
                    <textarea name="answer" id="questionAnswer" placeholder="Enter" style="width: 100%;min-height: 200px;"></textarea>
                </section>

                <!-- Navigation Buttons -->
                <div class="skip-next">
                    <button type="button" class="skip-btn" onclick="javascript:void(0); handleSkip();">Skip this
                        Question</button>
                    <button type="button" class="next-btn" onclick="javascript:void(0); submitAnswer();">NEXT <img
                            src="{{ getImgUrl('/img/arrow-back.png') }}" alt=""></button>
                </div>
            </form>
        </div>
    </main>

    <!-- retrieve container -->
    <!-- class active add here for showing this block -->
    <main role="main" class="retrieve-container {{ @$response['custom_error'] ? 'active' : '' }}">
        <div class="completion-box common-container" aria-labelledby="levelCompleteHeading">
            <p>
                {{ @$response['custom_error'] }}
            </p>
            <a href="#" class="retrivebtn ctabtn" id="retrivebtn">Retry</a>
        </div>
    </main>

    <div id="toast">Please <span>select an option</span> or <span>skip the question</span> to proceed.</div>
    @php
        $assessmentId = session()->get('assessment_id');
        $encodedAssessmentId = base64_encode($assessmentId);
        //p($response['question_messages']);
    @endphp
    <script>
        var mobile_number = "<?php echo session()->get('mobile_number'); ?>";
        var AssessId = "<?php echo $encodedAssessmentId; ?>";
        var question_messages = <?php echo json_encode($response['question_messages']); ?>;
        var skip_messages = <?php echo json_encode($response['skip_messages']); ?>;
        localStorage.setItem('isEnd', '0');
        // question watch javascript
        const stopwatchElement = document.getElementById('stopwatchTimer');
        let elapsed = 0;
        let stopwatchInterval = null;

        function startStopwatch() {
            if (stopwatchInterval) return; // Already running
            stopwatchInterval = setInterval(() => {
                elapsed++;
                let mins = Math.floor(elapsed / 60).toString().padStart(2, '0');
                let secs = (elapsed % 60).toString().padStart(2, '0');
                stopwatchElement.textContent = `${mins}:${secs}`;
            }, 1000);
            console.log("▶️ Stopwatch Started");
        }

        function stopStopwatch() {
            clearInterval(stopwatchInterval);
            stopwatchInterval = null;
            console.log("⏰ Stopwatch Stopped at", elapsed, "seconds");
        }
        $(function() {
            if ($("#question_id").val()) {
                startStopwatch();
            }
            $('input[name="answer"]').on('click', function() {
                $(".next-btn").addClass("active");
            });
            $('textarea[name="answer"]').on('keyup', function() {
                if ($(this).val().trim() != "") {
                    $(".next-btn").addClass("active");
                } else {
                    $(".next-btn").removeClass("active");
                }
            });
            $("#retrivebtn").click(function() {
                $(".retrieve-container").removeClass("active");
                $("#loader-msg").html("Please wait");
                $(".loader-wrap").addClass("active");
                $(".main-question-screen").addClass("hide");
                location.reload();
            });
            let question_type = $("#question_type").val();
            if (question_type == "MCQ") {
                $("#mcq_wrap").show();
                $("#coding_wrap").hide();
            } else {
                $("#coding_wrap").show();
                $("#mcq_wrap").hide();
            }
        });

        function submitAnswer() {
            let question_type = $("#question_type").val();
            //console.log("question_type",question_type);
            if (question_type == "MCQ") {

                if (!$('input[name="answer"]:checked').length) {
                    $(".next-btn").removeClass("active");
                    $("#toast").html("Please <span>select an option</span> or <span>skip the question</span> to proceed.");
                    toast.classList.add('show');
                    setTimeout(() => {
                        toast.classList.remove('show');
                    }, 10000);
                    return false;
                } else {
                    $(".next-btn").addClass("active");
                }
            } else {
                let questionAnswer = $('textarea[name="answer"]').val().trim();
                if (!questionAnswer) {
                    $("#toast").html("Please <span>write your answer</span> or <span>skip the question</span> to proceed.");
                    toast.classList.add('show');
                    setTimeout(() => {
                        toast.classList.remove('show');
                    }, 10000);
                    return false;
                } else {
                    $(".next-btn").addClass("active");
                }
            }


            // Convert to a plain object (optional)
            const form = document.getElementById('questionForm');
            const formData = new FormData(form);
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });
            if (question_type == "MCQ") {
                data["answer"] = $('input[name="answer"]:checked').val();
            } else {
                data["answer"] = $('textarea[name="answer"]').val();
            }
            data["time_taken"] = elapsed;
            //localStorage.setItem('time_taken', elapsed);
            let currentQNo = $("#current_question").text();
            if (currentQNo == 1) {
                data["time_taken"] = elapsed;
            } else {
                data["time_taken"] = elapsed - localStorage.getItem('time_taken');
            }
            localStorage.setItem('time_taken', elapsed);

            //debugger;
            if($("#current_question").text()==$("#total_question").text()){
                $("#loader-msg").html("Please wait, your assessment report is generating.");
            }
            else{
                $("#loader-msg").html(question_messages[currentQNo - 1]);
            }
            //$("#loader-msg").html(question_messages[currentQNo - 1]);

            $(".loader-wrap").addClass("active");
            $(".main-question-screen").addClass("hide");
            stopStopwatch();

            const url = base_url + '/lets-start/step-1';
            fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': "{{ csrf_token() }}",
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status_code === 200) {
                        startStopwatch();
                        const questionDetail = data.response[0].question;
                        const current_question = data.response[0].current_question;
                        const total_question = data.response[0].total_question;
                        const question_id = data.response[0].question_id;
                        //console.log(total_question, ' == ', current_question);
                        if ((current_question == total_question) && !questionDetail) {
                            localStorage.setItem('isEnd', '1');
                            localStorage.removeItem('inTest');
                            localStorage.removeItem('time_taken');
                            setTimeout(() => {
                                
                                if(data.response[0].history.length === 15 && mobile_number == ''){
                                    window.location.href = base_url + '/unlock-your-score/';
                                }else{
                                    window.location.href = base_url + '/assessment-report/' + AssessId
                                }


                            }, 1000);

                        } else {
                            $(".loader-wrap").removeClass("active");
                            $(".main-question-screen").removeClass("hide");
                        }
                        displayNextQuestion(questionDetail, current_question, question_id);
                    } else {
                        $(".loader-wrap").removeClass("active");
                        $(".main-question-screen").removeClass("hide");
                        $("#toast").html(
                            "Oops! <span>Some error occur</span> <span>, please click on next again.</span>");
                        toast.classList.add('show');
                        setTimeout(() => {
                            toast.classList.remove('show');
                        }, 5000);
                        return false;
                        //console.error('Unexpected response', data);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        function handleSkip() {
            // Convert to a plain object (optional)
            const form = document.getElementById('questionForm');
            const formData = new FormData(form);
            const data = {};
            formData.forEach((value, key) => {
                data[key] = value;
            });
            data["answer"] = "SKIP";
            let currentQNo = $("#current_question").text();
            if (currentQNo == 1) {
                data["time_taken"] = elapsed;
            } else {
                data["time_taken"] = elapsed - localStorage.getItem('time_taken');
            }
            localStorage.setItem('time_taken', elapsed);

            let randomNumber = Math.floor(Math.random() * 4);
            if($("#current_question").text()==$("#total_question").text()){
                $("#loader-msg").html("Please wait, your assessment report is generating.");
            }
            else{
                $("#loader-msg").html(skip_messages[randomNumber]);
            }

            $(".loader-wrap").addClass("active");
            $(".main-question-screen").addClass("hide");
            const url = base_url + '/lets-start/step-1';
            stopStopwatch();
            fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': "{{ csrf_token() }}",
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(data => {
                    startStopwatch();
                    if (data.status_code === 200) {
                        const questionDetail = data.response[0].question;
                        const current_question = data.response[0].current_question;
                        const total_question = data.response[0].total_question;
                        const question_id = data.response[0].question_id;
                        //console.log(total_question,' == ',current_question);
                        if ((current_question == total_question) && !questionDetail) {
                            localStorage.setItem('isEnd', '1');
                            localStorage.removeItem('inTest');
                            setTimeout(() => {
                                window.location.href = base_url + '/assessment-report/' + AssessId
                            }, 1000);
                        } else {
                            $(".loader-wrap").removeClass("active");
                            $(".main-question-screen").removeClass("hide");
                        }
                        displayNextQuestion(questionDetail, current_question, question_id);
                    } else {
                        $(".loader-wrap").removeClass("active");
                        $(".main-question-screen").removeClass("hide");
                        $("#toast").html(
                            "Oops! <span>Some error occur</span> <span>, please click on next again.</span>");
                        toast.classList.add('show');
                        setTimeout(() => {
                            toast.classList.remove('show');
                        }, 5000);
                        return false;
                        //console.error('Unexpected response', data);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        function displayNextQuestion(questionDetail, current_question, question_id) {
            $('textarea[name="answer"]').val('')
            if (question_type == "MCQ") {
                $("#mcq_wrap").show();
                $("#coding_wrap").hide();
            } else {
                $("#coding_wrap").show();
                $("#mcq_wrap").hide();
            }
            $(".next-btn").removeClass("active");
            //console.log('Next Question: ', questionDetail);
            $("input[name='answer']").prop('checked', false);
            $("#question_title").html(questionDetail.question);
            $("#div_opt_a").text(questionDetail.option_A);
            $("#div_opt_b").text(questionDetail.option_B);
            $("#div_opt_c").text(questionDetail.option_C);
            $("#div_opt_d").text(questionDetail.option_D);
            $("#current_question").html(current_question);
            $("#question_type").val(questionDetail.question_type);
            $("#cq").val(current_question);
            $("#question_id").val(question_id);
            if (questionDetail.question_type == "MCQ") {
                $("#mcq_wrap").show();
                $("#coding_wrap").hide();
            } else {
                $("#coding_wrap").show();
                $("#mcq_wrap").hide();
            }
        }
    </script>
    <script>
    const GAME_END_URL = base_url + "/lets-start";

    // Detect if this page load is a reload or typed-in navigation while in test
    function isReloadOrAddressBarWhileInTest() {
        const navType = (performance.getEntriesByType && performance.getEntriesByType("navigation")[0])
            ? performance.getEntriesByType("navigation")[0].type
            : (performance.navigation ? (performance.navigation.type === 1 ? "reload" : "navigate") : "navigate");

        // Reload detected OR already in test before page load
        return navType === "reload" || sessionStorage.getItem('wasInTest') === 'true';
    }

    if (localStorage.getItem('isEnd') !== "1") {
        if (isReloadOrAddressBarWhileInTest() && localStorage.getItem('inTest') === 'true') {
            localStorage.removeItem('inTest');
            localStorage.setItem('isEnd', '1');
            sessionStorage.removeItem('wasInTest');
            window.location.href = GAME_END_URL;
        }

        window.onload = function () {
            localStorage.setItem('inTest', 'true');
            sessionStorage.setItem('wasInTest', 'true'); // Persist only for this tab/session
        };

        window.addEventListener('beforeunload', function (e) {
            if (localStorage.getItem('inTest') === 'true') {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    }
</script>
@endsection
