<template>
    <div>
      <loader :isVisible="isLoader" text="loading"/>
      <section class="logout-content pt-4 pb-3">
        <div class="container">
          <div class="row">
            <div class="col-12 text-center">
              <span class="success-message">You have successfully logged out</span>
              <img src="{{getFrontendImg('/assets/mark_assets/images/intro-logo.png')}}" alt="" class="logo mb-3">
              <h2>MGL</h2>
              <p>Play games & win coins <img src="{{getFrontendImg('/assets/mark_assets/images/small-coins-icon.png')}}" alt=""></p>
              <router-link :to="'/signup'" v-if="pageLoadType === 'false'" tag="a"  class="text-center">Login again to win coins</router-link>
                          <a href="'/signup'" v-if="pageLoadType === 'true'"  class="text-center">Login again to win coins</a>

              <!-- <a href="javascript:void(0);" class="text-center">Login again to win coins</a> -->
            </div>
          </div>
        </div>
      </section>

      <!-- # Logout Screen -->
      <section class="allgames_mygames_tabs">
        <div class="container">
          <div class="row">
            <div class="col-12">

              <div class="cards-group">
                <!-- Card Box Start -->
                <div class="cards-group">
                    <!-- Card Box Start -->
                    <div v-for="(game, index) in all_games" v-if="game.consolidate_high_score.total_contest > 0 && game.seo_url !=null" >
                      <div class="card-box">
                        <div class="form-row d-flex align-items-center head-texts">
                          <div class="col-6"><h6>{{game.game_name}}</h6></div>
                          <div class="col-6 active-users text-right"><p><span class="active-sign"></span>{{numberWithCommas(game.consolidate_high_score.total_users_playing)}} users</p></div>
                        </div>
                        <div class="form-row main-content d-flex align-items-center">
                          <div class="col-8 align-items-center d-flex left-cols">
                            <div class="image-area">
                              <img  :src="getImgUrl(game.logo)" v-bind:alt="game.game_name">
                            </div>
                            <div class="content">
                              <h4>Play & Win: </h4>
                              <h3><img src="{{getFrontendImg('/assets/mark_assets/images/medium-coins-icon.png')}}" alt=""> {{numberWithCommas(game.consolidate_high_score.total_prize_money)}}</h3>
                            </div>
                          </div>
                          <div class="col-4 right-cols">
                             <router-link :to="'contests/'+game.seo_url" tag="a" v-if="pageLoadType === 'false'">Join A Contest</router-link>
                             <a v-if="pageLoadType === 'true'" href='contests/' + game.seo_url>Join A Contest</a>

                            <h6>Contest Running: <span>{{game.consolidate_high_score.total_contest}}</span></h6>
                          </div>
                        </div>
                        
                      </div>

                      <!--<div class="card-box" v-if="index == 0">
                        <div class="form-row">
                          <div class="col-12">
                             <Googlead pageCode="MGL_01"/>
                            <img src="/assets/mark_assets/images/ads-placemet.jpg" alt="" class="w-100">
                          </div>
                        </div>
                      </div>-->
                    </div>
                    <!-- Card Box End -->
                  </div>
            
                <!-- Card Box End -->
              </div>
              
            </div>
          </div>
        </div>
      </section>
      
    </div>
</template>
 
<script>
import Loader from "./../common/Loader.vue";
import Googlead from "./../common/Googlead.vue";
import common from './../common';
  export default {
    components: {
        Loader,Googlead
        },
      data(){
          return {
            isLoader:false,
            all_games:[],
            cdn_path:"",
            prize_breakup:[],
             pageLoadType:false,
          }
      },
      created: function() {


                                  this.pageLoadType = this.$pageLoadType;

          this.isUserAuthenticate = common.isUserAuthenticate.bind(this);
          this.clearUserRankStorage = common.clearUserRankStorage.bind(this);
          this.adNetworkManipulation = common.adNetworkManipulation.bind(this);
          this.showMessagePopup = common.showMessagePopup.bind(this);
          this.formatAMPM = common.formatAMPM.bind(this);
          this.showPrizeBreakup = common.showPrizeBreakup.bind(this);
          this.loopTimer = common.loopTimer.bind(this);
          this.getImgUrl = common.getImgUrl.bind(this);
          this.convertDateToTime = common.convertDateToTime.bind(this);
          this.numberWithCommas = common.numberWithCommas.bind(this);
          this.timeDiffCalc = common.timeDiffCalc.bind(this);

          this.isUser = this.isUserAuthenticate();

          let self = this;
          this.isLoader=true;
           var response =  this.$httpClient.get('logout').then(function(res){
            // console.log('results:');
            // console.log(res.data);
              
                
                this.$cookies.remove('cutk');
                this.$cookies.remove('ustk');
                this.$cookies.remove('cutka');
                this.$cookies.remove('user_id');
                this.$cookies.remove('ubal');
                this.$cookies.remove('uvisit');
                this.clearUserRankStorage();
                // self.$router.push({ name: 'home'}) 
                
               self.isLoader=false;
           });


          self.isLoader=true;
           var response =  this.$httpClient.get('games').then(function(res){
              if(res.data.status_code==200)
               {
                self.all_games = res.data.response[0].games.games;
                self.cdn_path = res.data.response[0].games.cdn_path;
                if(res.data.response[0].ad_network!=undefined)
                {
                  self.adNetworkManipulation(res.data.response[0].ad_network);
                }

               }
              
               self.isLoader=false;
           });
        
        
      },
       methods: {
            
        }
    }
</script>