@extends('cms::layout')

@section('title', 'TechGig | Assessment Report')
@section('content')
    @php
        $assessmentId = $assessment_id;
        $encodedAssessmentId = base64_encode($assessmentId);
    @endphp
    <style>
        .wrapper header {
            position: relative;
            display: flex;
            top: unset;
            left: unset;
            transform: unset;
        }
    </style>

    <!-- Main Content -->
    <main role="main" class="assessment-content">
        <div class="report-header">
            <div class="left">
                <a href="<?php echo URL::to('/lets-start?report_page'); ?>">
                    <img src="{{ getImgUrl('/img/left-icon.png') }}" alt="left icon" loading="lazy">
                </a>
                <h2>Assessment Report</h2>
            </div>

            @php
                $score = @$assessmentReportDetail['report']['total_score'] ?? 0;
            @endphp

            @if ($score <= 59)
                <button onclick="showPopup();" class="share_btn">
                    <img src="{{ getImgUrl('/img/share_icon.png') }}" alt="share icon" loading="lazy">
                    SHARE</button>
            @elseif ($score >= 60)
                <a href="{{ url('/lets-start?report_page') }}" class="ctabtn mob-retake">RETAKE ASSESSMENT</a>
            @endif
        </div>
        <div class="report-summary-card">
            <div class="profile-details">
                <div>
                    <h4>Job Role</h4>
                    <h5>
                        {{ $assessmentReportDetail['context']['jobrole']['role_name'] ?? '' }}
                    </h5>
                </div>
                <div>
                    <h4>Primary Skill</h4>
                    <h5>{{ $assessmentReportDetail['context']['assessment']['primary_skill'] ?? '' }}</h5>
                </div>
                <div>
                    <h4>Secondary Skill</h4>
                    <h5>{{ $assessmentReportDetail['context']['assessment']['secondary_skill'] ?? '' }}</h5>
                </div>
                <div>
                    <h4>Years of Experience</h4>
                    <h5>
                        @php
                            $expInYear = $assessmentReportDetail['context']['assessment']['exp_in_year'] ?? null;
                        @endphp

                        @if ($expInYear == 1)
                            0-1 Year
                        @elseif ($expInYear == 3)
                            3-5 Years
                        @elseif ($expInYear == 5)
                            5-7 Years
                        @elseif ($expInYear == 6)
                            7+ Years
                        @else
                            Unknown
                        @endif
                    </h5>
                </div>
            </div>
            <div class="report-details">
                <div>
                    <img src="{{ getImgUrl('/img/report-bg-mobile.png') }}" loading="lazy" alt="Report">
                    @php
                        $score = @$assessmentReportDetail['report']['total_score'] ?? 0;
                        $scoreClass = '';

                        if ($score >= 0 && $score <= 39) {
                            $scoreClass = 'text-beginner';
                        } elseif ($score >= 40 && $score <= 59) {
                            $scoreClass = 'text-pioneer';
                        } elseif ($score >= 60 && $score <= 79) {
                            $scoreClass = 'text-curious';
                        } elseif ($score >= 80 && $score <= 100) {
                            $scoreClass = 'text-ready';
                        }
                    @endphp

                    <h2 class="{{ $scoreClass }}">{{ $score }}</h2>
                    </h2>
                </div>
                <h3>You scored
                    <strong><span>{{ @$assessmentReportDetail['report']['total_score'] ? @$assessmentReportDetail['report']['total_score'] : '0' }}/100</span>
                        points</strong>
                </h3>
                @php
                    $tier = @$assessmentReportDetail['report']['readiness_tier'];
                    $score = @$assessmentReportDetail['report']['total_score'] ?? 0;
                    $scoreClass = '';

                    if ($score >= 0 && $score <= 39) {
                        $scoreClass = 'text-beginner';
                    } elseif ($score >= 40 && $score <= 59) {
                        $scoreClass = 'text-pioneer';
                    } elseif ($score >= 60 && $score <= 79) {
                        $scoreClass = 'text-curious';
                    } elseif ($score >= 80 && $score <= 100) {
                        $scoreClass = 'text-ready';
                    }
                @endphp

                <h4 class="{{ $scoreClass }}">
                    You are an {{ $tier ?? '' }}
                </h4>

                @php
                    $score = @$assessmentReportDetail['report']['total_score'] ?? 0;
                @endphp

                @if ($score <= 59)
                    <a href="{{ url('/lets-start?report_page') }}" class="ctabtn">RETAKE ASSESSMENT</a>
                    <button onclick="showPopup();" class="share_btn mobile_view mt-16">
                        <img src="{{ getImgUrl('/img/share_icon.png') }}" alt="share icon" loading="lazy">
                        SHARE</button>
                @elseif ($score >= 60)
                    <button onclick="showPopup();" class="share_btn">
                        <img src="{{ getImgUrl('/img/share_icon.png') }}" alt="share icon" loading="lazy">
                        SHARE
                    </button>
                    <a href="{{ url('/lets-start?report_page') }}" class="ctabtn mobile_view mt-16">RETAKE ASSESSMENT</a>
                @endif
            </div>
            <div class="report-tabs-container">
                <div class="report-tabs">
                    <div class="report-tab active" data-tab="strengths">Your Strengths</div>
                    <div class="report-tab" data-tab="improvement">Areas of Improvement</div>
                </div>

                <div class="report-tab-content active" id="strengths">
                    <div class="description">
                        {{ is_array(@$assessmentReportDetail['report']['strengths']['details'])
                            ? implode('  ', @$assessmentReportDetail['report']['strengths']['details'])
                            : '' }}
                    </div>
                    <ul class="checklist">
                        @foreach ($assessmentReportDetail['report']['strengths']['points'] ?? [] as $points)
                            <li><span>{{ $points }}</span></li>
                        @endforeach
                    </ul>
                </div>

                <div class="report-tab-content" id="improvement">
                    <div class="description">
                        {{ is_array(@$assessmentReportDetail['report']['improvement_areas']['details'])
                            ? implode('  ', @$assessmentReportDetail['report']['improvement_areas']['details'])
                            : '' }}
                    </div>
                    <ul class="checklist">
                        @foreach ($assessmentReportDetail['report']['improvement_areas']['points'] ?? [] as $points)
                            <li><span>{{ $points }}</span></li>
                        @endforeach
                    </ul>
                </div>

            </div>
        </div>

        @php
            $score = @$assessmentReportDetail['report']['total_score'] ?? 0;
        @endphp

        @if ($score >= 60 && $score <= 100)
            <div class="ai-resume-banner">
                <div class="left">
                    <img src="{{ getImgUrl('/img/ai-resume-banner.png') }}" loading="lazy" alt="Banner">
                    <div>
                        <h3>Add your AI Readiness Score in your Resume.</h3>
                        <h5>Craft an ATS-friendly resume in minutes, with AI-powered Resume Builder.</h5>
                    </div>
                </div>
                <a href="https://ai.techgig.com/ai-resume" target="_blank" class="ctabtn">CREATE RESUME</a>
            </div>

            <div class="mobile-ai-resume-banner">
                <div class="ai-resume-content">
                    <h3>Add your AI Readiness Score in your Resume.</h3>
                    <h5>Craft an ATS-friendly resume in minutes, with AI-powered Resume Builder.</h5>
                </div>
                <a href="https://ai.techgig.com/ai-resume" target="_blank" class="ctabtn">CREATE RESUME</a>
                <div class="left">
                    <img src="{{ getImgUrl('/img/ai-resume-banner.png') }}" loading="lazy" alt="Banner">
                </div>
            </div>
        @endif


        <div class="recommended-learning-paths">
            <h2>Recommended Learning Paths</h2>
            <p>
                {{ is_array(@$assessmentReportDetail['report']['recommended_learning_path']['details'])
                    ? implode('  ', @$assessmentReportDetail['report']['recommended_learning_path']['details'])
                    : '' }}
            </p>
            <ul>
                @foreach ($assessmentReportDetail['report']['recommended_learning_path']['points'] ?? [] as $points)
                    <li><span>{{ $points }}</span></li>
                @endforeach
            </ul>

        </div>
        @php
            $correctNo = 0;
            $incorrectNo = 0;
            $skippedNo = 0;
        @endphp

        @foreach (@$assessmentReportDetail['questionsList'] as $question)
            @php
                if ($question['user_answer'] == 'SKIP') {
                    $skippedNo++;
                } elseif ($question['is_correct'] == '1') {
                    $correctNo++;
                } else {
                    $incorrectNo++;
                }
            @endphp
        @endforeach

        <div class="assessment-container">

            <div class="assessment-header">
                <h2>Your Performance
                </h2>
                <div class="right">
                    <div>
                        <h5>Correct : <span class="correct-no">{{ $correctNo }}</span></h5>
                        <h5>Incorrect : <span class="incorrect-no">{{ $incorrectNo }}</span></h5>
                        <h5>Skipped : <span class="skipped-no">{{ $skippedNo }}</span></h5>
                    </div>
                    <button class="expand-btn"><img src="{{ getImgUrl('/img/expand_icon.png') }}"
                            loading="lazy" alt="Expand Icon"> Expand
                        All</button>
                </div>
                <button class="open-filterpopup-btn" onclick="openFilterPopup()">
                    <img src="{{ getImgUrl('/img/filter_icon.png') }}" alt="Filter Icon" loading="lazy" width="18">
                </button>
            </div>


            @foreach (@$assessmentReportDetail['questionsList'] as $questionKey => $question)
                @php
                    $questionClass = '';
                    $questionText = '';
                    if ($question['is_correct'] == '1') {
                        $questionClass = 'correct';
                        $questionText = 'correct';
                    } else {
                        if ($question['user_answer'] == 'SKIP') {
                            $questionClass = 'skipped';
                            $questionText = 'Skipped';
                        } else {
                            $questionClass = 'incorrect';
                            $questionText = 'incorrect';
                        }
                    }

                @endphp
                <div class="assessment-output-box">
                    <div class="assessmet-question-header" onclick="toggleCard(this)">
                        <div>
                            <h2>Question {{ $questionKey + 1 }}/{{ count($assessmentReportDetail['questionsList']) }}:
                            </h2>
                            <span class="badge-status {{ $questionClass }}">{{ ucfirst($questionText) }}</span>
                        </div>
                        <p class="question-text">
                            {{ $question['question'] }}
                        </p>
                    </div>

                    @if ($question['question_type'] == 'MCQ')
                        <div class="options-wrapper">
                            @foreach ($question['answer']['options'] as $key => $option)
                                @php
                                    $class = '';
                                    if ($question['correct_answer'] == $key && $question['user_answer'] == $key) {
                                        $class = 'correct';
                                    } elseif ($question['user_answer'] == $key) {
                                        $class = 'incorrect';
                                    } elseif ($question['correct_answer'] == $key) {
                                        $class = 'correct';
                                    }

                                @endphp
                                <div class="option-card {{ $class }}">
                                    <div class="option-label">{{ $key }}</div>
                                    {{ $option }}
                                </div>
                            @endforeach
                        </div>
                    @elseif ($question['question_type'] == 'CODING')
                        <div class="options-wrapper code-wrapper">
                            <div class="yourcode-wrapper">
                                <h6>
                                    Your Code
                                </h6>
                                <div class="yourcode-box">
                                    <pre style="text-wrap-mode: wrap;">{{ @$question['user_answer'] }}</pre>
                                </div>
                            </div>

                            <div class="explanation-box">
                                <h4>Correct Answer:</h4>
                                <p>
                                    <pre style="text-wrap-mode: wrap;">{{ @$question['correct_answer'] }}</pre>
                                </p>
                            </div>
                        </div>
                    @endif
                </div>
            @endforeach

        </div>
    </main>


    {{-- ai readiness popup ui --}}
    <div class="ai-readiness-score-popup">
        <div class="inner-readiness-content">
            <button class="closeBtn">
                <img src="{{ getImgUrl('/img/close_btn.png') }}" alt="close icon" loading="lazy">
            </button>
            <h2>Share your <strong>AI-Readiness Score</strong></h2>
            <div class="readiness-content">
                <div class="readiness-card">
                    <img src="{{ getImgUrl('/img/TechGigLogo.png') }}" alt="TechGIG logo" width="80px"
                        loading="lazy">
                    <div class="readiness-report">
                        <h3>AI Readiness Test</h3>
                        <img src="{{ getImgUrl('/img/report-bg-mobile.png') }}" alt="">
                        <div class="report-content">

                            @php
                                $score = @$assessmentReportDetail['report']['total_score'] ?? 0;
                                $scoreClass = '';

                                if ($score >= 0 && $score <= 39) {
                                    $scoreClass = 'text-beginner';
                                } elseif ($score >= 40 && $score <= 59) {
                                    $scoreClass = 'text-pioneer';
                                } elseif ($score >= 60 && $score <= 79) {
                                    $scoreClass = 'text-curious';
                                } elseif ($score >= 80 && $score <= 100) {
                                    $scoreClass = 'text-ready';
                                }
                            @endphp

                            <h4 class="{{ $scoreClass }}" id="score">{{ $score }}</h2>
                            </h4>
                            <h5>I just scored
                                <strong>{{ @$assessmentReportDetail['report']['total_score'] ? @$assessmentReportDetail['report']['total_score'] : '0' }}/100</strong>
                                on the <br> AI Readiness Test!
                            </h5>
                        </div>
                    </div>
                    @php
                        $tier = @$assessmentReportDetail['report']['readiness_tier'];
                        $score = @$assessmentReportDetail['report']['total_score'] ?? 0;
                        $scoreClass = '';

                        if ($score >= 0 && $score <= 39) {
                            $scoreClass = 'beginner';
                        } elseif ($score >= 40 && $score <= 59) {
                            $scoreClass = 'pioneer';
                        } elseif ($score >= 60 && $score <= 79) {
                            $scoreClass = 'curious';
                        } elseif ($score >= 80 && $score <= 100) {
                            $scoreClass = 'ready';
                        }
                    @endphp
                    <div class="{{ $scoreClass }} tagmarks">I'm an {{ $tier ?? '' }}</div>
                </div>

                <div class="share-options">
                    <p>Show-off your AI-Readiness Score and inspire your network to see where you stand.</p>
                    <div class="buttons">
                        <button class="linkedin"  id="linkedinShareBtn"><img src="{{ getImgUrl('/img/linkedin_icon.png') }}" loading="lazy"
                                alt="linkedin icon">
                            Share on
                            LinkedIn</button>
                        <button class="whatsapp" id="whatsappShareBtn"><img src="{{ getImgUrl('/img/whatsapp_icon.png') }}" loading="lazy"
                                alt="whatsapp icon">
                            Share on
                            WhatsApp</button>
                    </div>
                    <div class="line-divider">
                        <span></span>
                        OR
                        <span></span>
                    </div>
                    <h6>Copy Link and Share</h6>
                    <div class="copy-box">
                        <input type="text" value="<?php echo URL::to('/public-report') . '/' . $encodedAssessmentId; ?>" readonly>
                        <button onclick="navigator.clipboard.writeText('<?php echo URL::to('/public-report') . '/' . $encodedAssessmentId; ?>')">
                            <img src="{{ getImgUrl('/img/content_copy.png') }}" alt="Copy Icon" loading="lazy"
                                width="20">
                        </button>
                    </div>

                    <!-- Copied Toast -->
                    <div id="copiedToast" class="copied-toast">Copied to clipboard.</div>
                </div>
            </div>
        </div>
    </div>

    {{-- sticky rating box --}}
    <div class="rating-box" style="display:none;">
        <div>
            <h3>Rate Your Experience</h3>
            <h4>How would you rate the assessment you took just now?</h4>
        </div>
        <div class="rating-container" id="starRating">
            <div class="star-box" data-rating="1">
                <span class="star">&#9733;</span>
                <div class="star-label">BAD</div>
            </div>
            <div class="star-box" data-rating="2">
                <span class="star">&#9733;</span>
                <div class="star-label">OK</div>
            </div>
            <div class="star-box" data-rating="3">
                <span class="star">&#9733;</span>
                <div class="star-label">GOOD</div>
            </div>
            <div class="star-box" data-rating="4">
                <span class="star">&#9733;</span>
                <div class="star-label">GREAT</div>
            </div>
            <div class="star-box" data-rating="5">
                <span class="star">&#9733;</span>
                <div class="star-label">WOW</div>
            </div>
        </div>
    </div>

    {{-- Filter popup ui --}}
    <div class="popup-overlay" id="filterPopupOverlay">
        <div class="filter-popup">
            <span class="filter-close-btn" onclick="closeFilterPopup()">&times;</span>
            <h3>Filter</h3>
            <p>Correct Answers : <span class="correct-no">{{ $correctNo }}</span></p>
            <hr>
            <p>Incorrect Answers : <span class="incorrect-no">{{ $incorrectNo }}</span></p>
            <hr>
            <p>Skipped Answers : <span class="skipped-no">{{ $skippedNo }}</span></p>
            <hr>
            <div class="expand">
                <button class="expand-btn"><img src="{{ getImgUrl('/img/expand_icon.png') }}"
                        loading="lazy" alt="Expand Icon"> Expand
                    All</button>
            </div>
        </div>
    </div>

    {{-- Rating popop UI --}}
    <div class="popup-overlay" id="ratingPopup">
        <div class="inner-rating-content">
            <button class="down-btn" onclick="closeRatingPopup()">
                <img src="{{ getImgUrl('/img/keyboard-arrow_01Aug.png') }}" alt="Collapse Btn" loading="lazy">
            </button>
            <h4>Rate Your Experience</h4>
            <p>How would you rate the assessment you took just now?</p>
            <div class="rating-sec">
                <div>
                    <span data-rating="1">★</span>
                    <h5>BAD</h5>
                </div>
                <div>
                    <span data-rating="2">★</span>
                    <h5>OK</h5>
                </div>
                <div>
                    <span data-rating="3">★</span>
                    <h5>GOOD</h5>
                </div>
                <div>
                    <span data-rating="4">★</span>
                    <h5>GREAT</h5>
                </div>
                <div>
                    <span data-rating="5">★</span>
                    <h5>WOW</h5>
                </div>
            </div>
            <textarea name="feedbackInput" id="feedbackInput" placeholder="Share Feedback..."></textarea>

            <div class="ctabtn-container">
                <a href="javascript:void(0);" class="ctabtn" id="submitReviewBtn">SUBMIT REVIEW</a>
            </div>
        </div>
    </div>

    <script>
        var feedback = "<?php echo $feedback; ?>";
        var encodedAssessmentId = "<?php echo $encodedAssessmentId; ?>";
        let ispopupSubmited = false;
        if (!feedback) {
            setTimeout(function() {
                $(".rating-box").css({
                    opacity: 0,
                    display: 'flex'
                }).animate({
                    opacity: 1
                }, 400);
            }, 5000);
        }
        const reportTabs = document.querySelectorAll('.report-tab');
        const tabsContents = document.querySelectorAll('.report-tab-content');
        const copiedtoast = document.getElementById('copiedToast');


        reportTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all
                reportTabs.forEach(t => t.classList.remove('active'));
                tabsContents.forEach(c => c.classList.remove('active'));

                // Add active class to selected tab
                tab.classList.add('active');
                const target = document.getElementById(tab.getAttribute('data-tab'));
                target.classList.add('active');
            });
        });


        document.querySelector('.copy-box button').addEventListener('click', function() {
            const input = document.querySelector('.copy-box input');
            input.select();
            document.execCommand('copy');
            copiedtoast.classList.add('show');
            setTimeout(() => {
                copiedtoast.classList.remove('show');
            }, 3000);
        });


        const popupReadinessScore = document.querySelector('.ai-readiness-score-popup');
        const closeBtn = document.querySelector('.closeBtn');

        // Show popup 
        function showPopup() {
            popupReadinessScore.classList.add('show');
        }

        // Hide popup on close
        closeBtn.addEventListener('click', () => {
            popupReadinessScore.classList.remove('show');
        });

        function toggleCard(header) {
            const card = header.closest('.assessment-output-box');
            card.classList.toggle('active');
            updateExpandCollapseBtn();
        }

        const expandCollapseBtns = document.querySelectorAll(".expand-btn");
        const assessmentOutputBoxes = document.querySelectorAll(".assessment-output-box");
        const array_assessmentOutputBoxes = Array.from(assessmentOutputBoxes);
        const filterPopupOverlay = document.getElementById("filterPopupOverlay");
        const ratingPopup = document.getElementById('ratingPopup');
        const ratingBox = document.querySelector(".rating-box");
        let isExpanded = false;

        // Update the button UI based on current state
        function updateExpandCollapseBtn() {
            const allActive = array_assessmentOutputBoxes.every(el => el.classList.contains("active"));
            isExpanded = allActive;

            expandCollapseBtns.forEach(btn => {
                btn.innerHTML = isExpanded ?
                    '<img src="../img/collapse_icon.png" loading="lazy" alt="Collapse Icon"> Collapse All' :
                    '<img src="../img/expand_icon.png" loading="lazy" alt="Expand Icon"> Expand All';
            });
        }

        // Expand or collapse all cards based on the toggle state
        function toggleAllCards(shouldExpand) {
            array_assessmentOutputBoxes.forEach(el => {
                el.classList.toggle("active", shouldExpand);
            });
            isExpanded = shouldExpand;
            updateExpandCollapseBtn();
        }

        // Attach click listener to all .expand-btn buttons
        expandCollapseBtns.forEach(btn => {
            btn.addEventListener("click", () => {
                toggleAllCards(!isExpanded);
                if (filterPopupOverlay) {
                    setTimeout(() => {
                        filterPopupOverlay.style.display = 'none';
                    }, 1200);
                }
            });
        });

        // Initial update to match current DOM state
        updateExpandCollapseBtn();


        // functions for filter popup
        function openFilterPopup() {
                filterPopupOverlay.style.display = 'flex';
            filterPopupOverlay.classList.add("show");
        }

        function closeFilterPopup() {
            filterPopupOverlay.classList.remove("show");
                filterPopupOverlay.style.display = 'none';
        }

        // Rating box functionality code
        const stars = document.querySelectorAll('.star-box');
        stars.forEach((star, index) => {
            star.addEventListener('click', () => {
                stars.forEach((s, i) => {
                    s.classList.toggle('selected', i <= index);
                    const rating = index + 1;
                    setTimeout(() => {
                        ratingFunction(rating);
                    }, 1000);
                });
            });
        });

        var ratingNumber;
        async function ratingFunction(rating) {
            if (rating <= 3) {
                ratingPopup.classList.add("show");
                ratingBox.style.display = "none";
                ratingNumber = rating;
                const popupRatingItems = document.querySelectorAll('.rating-sec div');
                popupRatingItems.forEach((el, i) => {
                    el.classList.toggle('selected', i < rating);
                });
            } else {
                const result = await sendPostData(rating, null, true);
            }
        }



        function closeRatingPopup() {
            ratingPopup.classList.remove("show");
            if (!ispopupSubmited) {
                stars.forEach((s, i) => {
                    s.classList.remove('selected');
                });
                $(".rating-box").css({
                    opacity: 0,
                    display: 'flex'
                }).animate({
                    opacity: 1
                }, 400);
            }
        }

        // Rating popup functionality code
        const ratingItems = document.querySelectorAll('.rating-sec div');
        ratingItems.forEach((item, index) => {
            item.addEventListener('click', (e) => {
                ratingItems.forEach((el, i) => {
                    el.classList.toggle('selected', i <= index);
                    ratingNumber = e.target.getAttribute("data-rating");
                });
            });
        });

        document.getElementById("whatsappShareBtn").addEventListener("click", function() {
            const aiScore = document.getElementById("score").innerText;

            const message =
                `I just took my AI Readiness Assessment and scored ${aiScore}/100.\n\n` +
                `Take your FREE assessment here :  ${base_url}/public-report/${encodedAssessmentId}?utm_source=WhatsApp&utm_campaign=sharedreport`;

            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;

        //     // Open WhatsApp share in a new tab
            window.open(whatsappUrl, "_blank");
        });

        document.getElementById("linkedinShareBtn").addEventListener("click", function() {
            const aiScore = document.getElementById("score").innerText;

            const linkedInShareUrl =
                base_url+"/public-report/"+encodedAssessmentId+"?utm_source=LinkedIn&utm_campaign=sharedreport&score=" + aiScore;

            const fullLinkedInUrl =
                `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(linkedInShareUrl)}`;

            // Open LinkedIn share window
            window.open(fullLinkedInUrl, "_blank");
        });

        document.getElementById("submitReviewBtn").addEventListener("click", () => {
            const getFeedbackValue = document.getElementById("feedbackInput").value;
            const ratingStar = Number(ratingNumber);
            console.log("Rating Number_____", ratingStar);
            console.log("FeedbackValue_____", getFeedbackValue);
            $("#submitReviewBtn").html("Submiting...");
            sendPostData(ratingStar, getFeedbackValue, false);

        });

        let hasSubmitted = false;

        async function sendPostData(param1, param2, fromRating = true) {
            const url = base_url + '/assessment-submit-feedback';
            if (hasSubmitted) return; // block duplicate submissions
            hasSubmitted = true;
            try {
                const data = {
                    rating: param1,
                    feedback: param2
                };

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': "{{ csrf_token() }}" // Only works in Blade
                    },
                    body: JSON.stringify(data)
                });

                let result;

                try {
                    result = await response.json(); // try to parse JSON
                } catch (jsonErr) {
                    console.error('Invalid JSON response:', jsonErr);
                    return {
                        status_code: response.status,
                        status: 'error',
                        message: 'Invalid JSON response'
                    };
                }

                if (!response.ok) {
                    if (fromRating) {
                        ratingBox.innerHTML = "<h6>Something went wrong. Please try again.</h6>";
                        setTimeout(function() {
                            $(".rating-box").css({
                                opacity: 0,
                                display: 'flex'
                            }).animate({
                                opacity: 1
                            }, 400);
                        }, 2000);
                    }
                    return {
                        status_code: response.status,
                        status: 'error',
                        message: result.message || 'Request failed'
                    };
                }
                if (fromRating) {
                    ratingBox.innerHTML = "<h6>Thanks for your great feedback!</h6>";
                    setTimeout(function() {
                        $(".rating-box").css({
                            opacity: 0,
                            display: 'none'
                        }).animate({
                            opacity: 1
                        }, 400);
                    }, 5000);
                } else {
                    $("#feedbackInput").val("");
                    ispopupSubmited = false;
                    closeRatingPopup();
                    ratingBox.innerHTML = "<h6>Thanks for your great feedback!</h6>";
                    setTimeout(function() {
                        $(".rating-box").css({
                            opacity: 0,
                            display: 'none'
                        }).animate({
                            opacity: 1
                        }, 400);
                    }, 5000);
                }

                return result; // ✅ Return result only when everything is OK

            } catch (error) {
                if (fromRating) {
                    ratingBox.innerHTML = "<h6>Something went wrong. Please try again.</h6>";
                    setTimeout(function() {
                        $(".rating-box").css({
                            opacity: 0,
                            display: 'flex'
                        }).animate({
                            opacity: 1
                        }, 400);
                    }, 2000);
                    //ratingBox.style.display = "none";
                }
                return {
                    status_code: 500,
                    status: 'error',
                    message: error.message
                };
            }
        }
    </script>
@endsection
