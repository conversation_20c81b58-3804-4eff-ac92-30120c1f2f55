<?php

namespace App\Http\Controllers\AdminAuth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CmsController extends Controller
{

    /**14583
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function __construct()
    {
        $this->middleware('admin-guest');
    }

   public function testEmail()
    {

            #Send Email to user
            $data = array();
            $data['url'] = 'https://admin.qureka.me/';
            $data['name'] = 'Anjali';
            $data['email'] = '<EMAIL>';
            $data['password'] = '123456';
            $data['fromEmail'] = '<EMAIL>';

            \Mail::send('emails.new_user_email', ['data'=>$data], function ($message) use ($data)
                {
                    $message->from($data['fromEmail'], 'CoolBoots Media');
                    $message->to('<EMAIL>');
                    $message->bcc(array('<EMAIL>',));
                    $message->subject('Qureka Lite Backend Creadentials');
                }); 

            echo "Email Sent. Check your inbox.";
        }   

   

   
}
