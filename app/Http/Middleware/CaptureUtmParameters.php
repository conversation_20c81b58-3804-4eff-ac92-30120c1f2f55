<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Symfony\Component\HttpFoundation\Response;

class CaptureUtmParameters
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if UTM parameters are present in the request
        $utmSource = $request->query('utm_source');
        $utmMedium = $request->query('utm_medium');
        $utmCampaign = $request->query('utm_campaign');

        // Set default values if no UTM parameters are present
        if (!$utmSource && !$utmMedium && !$utmCampaign) {
            $utmSource = 'Direct';
            $utmMedium = null;
            $utmCampaign = null;
        }

        // Store UTM parameters in cookies with 30-minute expiration
        $response = $next($request);

        // Only set cookies if we have UTM parameters or need to set defaults
        if ($utmSource !== null || $request->query('utm_source') !== null) {
            Cookie::queue('utm_source', $utmSource, 30); // 30 minutes
        }

        if ($utmMedium !== null || $request->query('utm_medium') !== null) {
            Cookie::queue('utm_medium', $utmMedium, 30); // 30 minutes
        }

        if ($utmCampaign !== null || $request->query('utm_campaign') !== null) {
            Cookie::queue('utm_campaign', $utmCampaign, 30); // 30 minutes
        }

        return $response;
    }
}
