<?php

namespace App\Http\Middleware;

use Closure;

class FrameGuard
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        //$request->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $request->headers->set('X-Frame-Options', 'DENY');

        return $next($request);
    }
}
