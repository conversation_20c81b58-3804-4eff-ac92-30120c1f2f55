<?php
namespace App\Auth;

use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Auth\UserProvider;
use Illuminate\Http\Request;
use Illuminate\Contracts\Session\Session;

class SocialGuard implements Guard
{
    protected $user;
    protected $provider;
    protected $session;
    protected $request;

    public function __construct(UserProvider $provider, Request $request, Session $session)
    {
        $this->provider = $provider;
        $this->request = $request;
        $this->session = $session;
    }

    public function check()
    {
        return !is_null($this->user());
    }

    public function guest()
    {
        return !$this->check();
    }

    public function user()
    {
        if (!is_null($this->user)) {
            return $this->user;
        }
        $id = $this->session->get('social_user_id');
        if ($id) {
            return $this->user = $this->provider->retrieveById($id);
        }
        return null;
    }

    public function id()
    {
        return optional($this->user())->getAuthIdentifier();
    }

    public function validate(array $credentials = [])
    {
        // Not used for social login
        return false;
    }

    public function setUser(\Illuminate\Contracts\Auth\Authenticatable $user)
    {
        $this->user = $user;
        $this->session->put('social_user_id', $user->getAuthIdentifier());
        return $this;
    }

    public function hasUser()
    {
        return !is_null($this->user);
    }
}
