<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Solarium\Client;

class SolariumServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    protected $defer = true;
    public function register()
    {
        //
        // $this->app->bind(Client::class, function ($app) {
        //      $adapter = new \Solarium\Core\Client\Adapter\Curl();
        //      $eventDispatcher = new \Symfony\Component\EventDispatcher\EventDispatcher;
        //     return new Client($adapter,$eventDispatcher,$app['config']['solarium']);
        // });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
     public function provides()
    {
        return [Client::class];
    }
}
