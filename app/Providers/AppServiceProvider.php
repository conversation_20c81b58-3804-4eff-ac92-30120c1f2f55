<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Auth;
use App\Auth\SocialGuard;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        \URL::forceRootUrl(config('app.url'));
        Auth::extend('social', function ($app, $name, array $config) {
            return new SocialGuard(
                Auth::createUserProvider($config['provider']),
                $app['request'],
                $app['session.store']
            );
        });
        if($this->app->environment('production')) {
            \URL::forceScheme('https');
        }
    }
}
