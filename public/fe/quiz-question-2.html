<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <title>AI Quiz Question | TechGIG</title>

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Mona+Sans:wght@400;600;700&display=swap" rel="stylesheet" />
    <!-- <link href="https://fonts.cdnfonts.com/css/ds-digital" rel="stylesheet"> -->
    <link rel="stylesheet" href="./fonts/font.css">


    <!-- External CSS -->
    <link rel="stylesheet" href="./css/style.css" />
    <link rel="stylesheet" href="./css/responsive.css">


    <!-- Favicon -->
    <link rel="icon" href="./img/favicon.ico" type="image/x-icon" />
</head>

<body>
    <section class="wrapper question-screen-container">
        <div class="wrp_container">

            <!-- Header -->
            <header>
                <a href="/" class="tg_logo" aria-label="TechGIG Home">
                    <img src="./img/TechGigLogo.png" alt="TechGIG logo" loading="lazy" />
                </a>
            </header>

            <main role="main">
                <div class="question-box common-container">


                    <div class="top-header-content">
                        <!-- Question Header -->
                        <div class="question-header">
                            <h3>Question 2/5:</h3>
                        </div>
                        <!-- Levels -->
                        <div class="levels-sec">
                            <div class="alarm-sec">
                                <img src="./img/alarm.png" alt="alarm">
                                <div id="stopwatchTimer">
                                    00:00
                                </div>
                            </div>
                            <div class="levels">
                                <label class="level completed">
                                    <input type="radio" disabled />
                                    Level 1
                                </label>
                                <label class="level active">
                                    <input type="radio" checked disabled />
                                    Level 2
                                </label>
                                <label class="level">
                                    <input type="radio" disabled />
                                    Level 3
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Question -->
                    <div class="question-title">
                        You're a Java developer looking to build AI-powered features into your application. What is the
                        most AI-ready step you can take next? You're a Java developer looking to build AI-powered
                        features into your application. What is the
                        most AI-ready step you can take next?
                    </div>
                    <div class="instruction">Please select an option</div>

                    <!-- Options -->
                    <div class="options">
                        <label class="option">
                            <input type="radio" name="answer" value="A" />
                            <div class="option-label">A</div>
                            <div>By suggesting specific use cases and best practices for custom hooks.</div>
                        </label>

                        <label class="option">
                            <input type="radio" name="answer" value="B" />
                            <div class="option-label">B</div>
                            <div>Explore Java-based AI/ML libraries like Deeplearning4j or use Python-based models via
                                REST APIs By automatically converting hooks to class components for better readability
                                By automatically converting hooks to class components for better readability By
                                automatically converting hooks to class components for better readability</div>
                        </label>

                        <label class="option">
                            <input type="radio" name="answer" value="C" />
                            <div class="option-label">C</div>
                            <div>Explore Java-based AI/ML libraries like Deeplearning4j or use Python-based models via
                                REST APIs By automatically converting hooks to class components for better readability
                                By automatically converting hooks to class components for better readability By
                                automatically converting hooks to class components for better readability</div>
                        </label>

                        <label class="option">
                            <input type="radio" name="answer" value="C" />
                            <div class="option-label">D</div>
                            <div>Focus only on improving your Java multithreading skills</div>
                        </label>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="skip-next">
                        <button class="skip-btn">Skip this Question</button>
                        <!-- just add active class for getting btn active -->
                        <button class="next-btn" onclick="showToast()">NEXT <img src="./img/arrow-back.png"
                                alt=""></button>
                    </div>
                </div>
                <button onclick="startStopwatch()">Start</button>
                <button onclick="stopStopwatch()">Stop</button>
            </main>
        </div>
    </section>

    <div id="toast">Please <span>select an option</span> or <span>skip the question</span> to proceed.</div>

    <script>
        const options = document.querySelectorAll('.option input');

        options.forEach(input => {
            input.addEventListener('change', () => {
                document.querySelectorAll('.option').forEach(option => {
                    option.classList.remove('clicked');
                });
                input.closest('.option').classList.add('clicked');
            });
        });

        function showToast() {
            const toast = document.getElementById('toast');

            // Show toast
            toast.classList.add('show');

            // Hide toast after 15 seconds
            setTimeout(() => {
                toast.classList.remove('show');
            }, 15000);
        }



        // question watch javascript
        const stopwatchElement = document.getElementById('stopwatchTimer');
        let elapsed = 0;
        let stopwatchInterval = null;

        function startStopwatch() {
            if (stopwatchInterval) return; // Already running
            stopwatchInterval = setInterval(() => {
                elapsed++;
                let mins = Math.floor(elapsed / 60).toString().padStart(2, '0');
                let secs = (elapsed % 60).toString().padStart(2, '0');
                stopwatchElement.textContent = `${mins}:${secs}`;
            }, 1000);
            console.log("▶️ Stopwatch Started");
        }

        function stopStopwatch() {
            clearInterval(stopwatchInterval);
            stopwatchInterval = null;
            console.log("⏰ Stopwatch Stopped at", elapsed, "seconds");
        }

    </script>

</body>

</html>