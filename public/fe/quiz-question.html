<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <title>AI Quiz Question | TechGIG</title>

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Mona+Sans:wght@400;600;700&display=swap" rel="stylesheet" />

    <!-- External CSS -->
    <link rel="stylesheet" href="./css/style.css" />
    <link rel="stylesheet" href="./css/responsive.css">


    <!-- Favicon -->
    <link rel="icon" href="./img/favicon.ico" type="image/x-icon" />
</head>

<body>
    <section class="wrapper question-screen-container">
        <div class="wrp_container">

            <!-- Header -->
            <header>
                <a href="/" class="tg_logo" aria-label="TechGIG Home">
                    <img src="./img/TechGigLogo.png" alt="TechGIG logo" loading="lazy" />
                </a>
            </header>

            <main role="main">
                <div class="question-box common-container">

                    <!-- Levels -->
                    <div class="levels">
                        <label class="level completed">
                            <input type="radio" disabled />
                            Level 1
                        </label>
                        <label class="level active">
                            <input type="radio" checked disabled />
                            Level 2
                        </label>
                        <label class="level">
                            <input type="radio" disabled />
                            Level 3
                        </label>
                    </div>

                    <!-- Question Header -->
                    <div class="question-header">
                        <h3>Question 2/5:</h3>
                    </div>

                    <!-- Question -->
                    <div class="question-title">
                       In the context of integrating GitHub Copilot with React JS development, which capability assists in maintaining code consistency across a large team project?
                    </div>

                    <form action="#" class="textarea-wrapper">
                        <textarea name="questionAnswer" id="questionAnswer" placeholder="Enter"></textarea>
                    </form>

                    <!-- Navigation Buttons -->
                    <div class="skip-next">
                        <button class="skip-btn">Skip this Question</button>
                        <button class="next-btn">NEXT <img src="./img/arrow-back.png" alt=""></button>
                    </div>
                </div>
            </main>
        </div>
    </section>

    <script>
        const options = document.querySelectorAll('.option input');

        options.forEach(input => {
            input.addEventListener('change', () => {
                document.querySelectorAll('.option').forEach(option => {
                    option.classList.remove('clicked');
                });
                input.closest('.option').classList.add('clicked');
            });
        });
    </script>

</body>

</html>