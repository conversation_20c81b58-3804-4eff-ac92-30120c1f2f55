<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Basic Meta Tags -->
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Title -->
    <title>AI Assessment Form | TechGIG</title>

    <!-- Preconnect & Font Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Mona+Sans:wght@400;600;700&display=swap" rel="stylesheet" />

    <!-- External CSS -->
    <link rel="stylesheet" href="./css/style.css" />
    <link rel="stylesheet" href="./css/responsive.css" />

    <!-- Favicon -->
    <link rel="icon" href="./img/favicon.ico" type="image/x-icon" />
</head>

<body>
    <section class="wrapper h-none">
        <div class="wrp_container">

            <!-- Header -->
            <header>
                <a href="/" class="tg_logo" aria-label="TechGIG Home">
                    <img src="./img/TechGigLogo.png" alt="TechGIG Home Logo" loading="lazy" width="120" height="auto" />
                </a>
            </header>

            <!-- Main Form -->
            <main role="main">
                <div class="form-wrapper">
                    <h2>Let's Begin</h2>
                    <p class="subheading">Power up your progress. <span></span> The AI future starts here.</p>
                    <form name="assessmentForm" action="#" method="post">
                        <div class="form-grid">
                            <!-- Profile -->
                            <div class="form-group">
                                <label for="profile">Please select your profile</label>
                                <select id="profile" name="profile" class="wrong" required>
                                        <option value="" disabled selected>Select</option>
                                    <option value="DevOps" selected>DevOps</option>
                                    <option value="Frontend">Frontend</option>
                                    <option value="Backend">Backend</option>
                                </select>
                                <p class="err-msg show">Please select your profile.</p>

                            </div>


                            <!-- Primary Skill -->
                            <div class="form-group">
                                <label for="primary-skill">Please select your primary skill</label>
                                <input type="text" id="primary-skill" name="primary-skill" placeholder="Enter"
                                    required />
                                <p class="err-msg show">Please enter your primary skill.</p>

                            </div>

                            <!-- Experience -->
                            <div class="form-group">
                                <label for="experience">Years of Experience</label>
                                <select id="experience" name="experience" required>
                                        <option value="" disabled selected>Select</option>
                                    <option value="0-1">0-1</option>
                                    <option value="1-3">1-3</option>
                                    <option value="3-5">3-5</option>
                                    <option value="5+">5+</option>
                                </select>
                                <p class="err-msg show">Please select your years of experience.</p>

                            </div>

                            <!-- Secondary Skill -->
                            <div class="form-group form-group-second">
                                <label for="secondary-skill">Please select your secondary skill</label>
                                <input type="text" id="secondary-skill" name="secondary-skill" placeholder="Enter" />
                            </div>
                        </div>

                        <!-- AI Tools Radio -->
                        <fieldset class="form-group form-group-second form-radio-grp">
                            <legend>Have you used any AI Tools before?</legend>
                            <div class="radio-group">
                                <label class="radio-option">
                                    <input type="radio" name="ai-tools" value="yes" checked />
                                    <span>Yes</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="ai-tools" value="no" />
                                    <span>No</span>
                                </label>
                            </div>
                        </fieldset>

                        <!-- Footer: Icons + Submit -->
                        <div class="form-footer">
                            <div class="footer-icons" aria-hidden="true">
                                <div>
                                    <img src="./img/1-icon.png" alt="Rocket Icon" loading="lazy" width="32"
                                        height="32" />
                                </div>
                                <div>
                                    <img src="./img/emoji.png" alt="Emoji Icon" loading="lazy" width="32" height="32" />
                                </div>
                                <div>
                                    <img src="./img/ChatGPT_logo.png" alt="ChatGPT Logo" loading="lazy" width="32"
                                        height="32" />
                                </div>
                            </div>
                            <button type="submit" class="submit-btn">START THE ASSESSMENT</button>
                        </div>
                    </form>
                </div>
            </main>

        </div>
    </section>

    <div class="loader-container ">
        <div class="loader"></div>
    </div>

</body>

</html>