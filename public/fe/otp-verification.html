<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OTP Verification | TechGIG</title>


    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Mona+Sans:wght@400;600;700&display=swap" rel="stylesheet" />

    <!-- External CSS -->
    <link rel="stylesheet" href="./css/style.css" />
    <link rel="stylesheet" href="./css/responsive.css">


    <!-- Favicon -->
    <link rel="icon" href="./img/favicon.ico" type="image/x-icon" />
</head>

<body>
    <section class="wrapper h-none-new">
        <div class="wrp_container">

            <!-- Header -->
            <header>
                <a href="/" class="tg_logo" aria-label="TechGIG Home">
                    <img src="./img/TechGigLogo.png" alt="TechGIG logo" loading="lazy" />
                </a>
            </header>

            <!-- Main Form -->
            <main role="main">
                <div class="otp-box common-container">
                    <img src="./img/one-time-password.png" alt="Phone OTP Icon" loading="lazy" />

                    <h2>OTP Verification</h2>
                    <p>One Time Password (OTP) has been sent to
                        <span class="mobile-number">999XXXXXXX
                            <a href="" class="edit-icon" aria-label="Edit mobile number">
                                <img src="./img/edit.png" alt="Edit mobile number" loading="lazy">
                            </a>
                        </span>
                    </p>

                    <!-- OTP Form -->
                    <form onsubmit="return validateOTP(event)">
                        <div class="form-container">
                            <input type="text" id="otp" name="otp" placeholder="Enter your OTP" maxlength="4"
                                aria-describedby="otpError" required inputmode="numeric" />
                            <div class="otp-error-msg" style="display: none;">
                                <img src="./img/cross.png" alt="Error icon" class="error-icon" />
                                <span>Invalid OTP</span>
                            </div>
                        </div>

                        <button type="submit">SUBMIT</button>
                    </form>

                    <!-- Timer -->
                    <div class="resend">
                        Resend OTP in : <span class="timer">00:10</span>
                    </div>

                    <!-- Resend Link -->
                    <div class="resend-container">
                        <a href="#" id="resendBtn">
                            Resend OTP
                        </a>
                    </div>
                </div>
            </main>
        </div>
    </section>

    <!-- JavaScript -->
    <script>
        let timerDisplay = document.querySelector('.timer');
        let resendBox = document.querySelector('.resend');
        let resendContainer = document.querySelector('.resend-container');


        let countdown = 10;
        let timerInterval = setInterval(() => {
            countdown--;
            let seconds = countdown < 10 ? '0' + countdown : countdown;
            timerDisplay.textContent = `00:${seconds}`;

            if (countdown <= 0) {
                clearInterval(timerInterval);
                resendBox.style.display = "none";
                resendContainer.style.display = "flex";
            }
        }, 1000);


        function validateOTP(event) {
            event.preventDefault();

            const enteredOTP = document.getElementById("otp").value;
            const otpInput = document.getElementById("otp");
            const errorBox = document.querySelector(".otp-error-msg");

            // Mock correct OTP
            const correctOTP = "1234";

            if (enteredOTP !== correctOTP) {
                // Show error styles
                otpInput.style.border = "1px solid #E01F26";
                errorBox.style.display = "flex";
                errorBox.style.justifyContent = "center";
                errorBox.style.alignItems = "center";
                errorBox.style.gap = "8px";
            } else {
                // Proceed with verification
                alert("OTP Verified!");
            }

            return false;
        }
    </script>


</body>

</html>