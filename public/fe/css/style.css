* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: "Mona Sans", sans-serif;
}

:root {
  --white: #fff;
  --black: #000;
  --red: #e01f26;
  --textColor: #1e497b;
  font-size: 10px;
  scroll-behavior: smooth;
}

.wrapper {
  background: url(../img/wrapper_bg.png) no-repeat center;
  background-size: cover;
  height: 100vh;
  width: 100%;
  padding: 0px 100px;
  overflow: hidden;
}

.wrp_container {
  width: 100%;
  margin: 0 auto;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none;
  padding: 0 15px;
}

.wrapper .wrp_container {
  padding: 40px 15px;
}

/* Extra small (xs): default - no media query needed */
.wrp_container {
  max-width: 100%;
}

/* Small (sm) ≥ 576px */
@media (min-width: 576px) {
  .wrp_container {
    max-width: 540px;
  }
}

/* Medium (md) ≥ 768px */
@media (min-width: 768px) {
  .wrp_container {
    max-width: 720px;
  }
}

/* Large (lg) ≥ 992px */
@media (min-width: 992px) {
  .wrp_container {
    max-width: 960px;
  }
}

/* Extra large (xl) ≥ 1200px */
@media (min-width: 1200px) {
  .wrp_container {
    max-width: 1140px;
  }
}

/* Extra extra large (xxl) ≥ 1400px */
@media (min-width: 1400px) {
  .wrp_container {
    max-width: 1320px;
  }
}

.wrapper header {
  position: absolute;
}

.wrapper main {
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
}

.tg_logo {
  text-decoration: none;
}

.tg_logo img {
  width: 112px;
  height: 40px;
  object-fit: contain;
}

.d_flex_mixin {
  display: flex;
  justify-content: center;
  align-items: center;
}

.common-container {
  background-color: var(--white);
  padding: 22px 35px;
  max-width: 999px;
  width: 100%;
  box-shadow: 0px 3px 10px #1e497b33;
  border-radius: 20px;
}

.main_content_container {
  max-width: 608px;
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 3px 10px #1e497b33;
  border-radius: 2rem;
  padding: 20px 66px;
  text-align: center;
}

.main_content_container h1 {
  color: #1e497b;
  font-size: 2.6rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.main_content_container .subtitle {
  color: #000000;
  font-weight: 500;
  font-size: 1.6rem;
}

.main_content_container p.terms {
  font-size: 1.4rem;
  color: #000000;
  font-weight: 400;
}

.main_content_container p.terms a {
  color: #e01f26;
  text-decoration: none;
}

.main_content_container .btn_container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin: 26px 0;
}

.main_content_container .btn_container a {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2.2rem;
  border: 1px solid #d6d6d6;
  border-radius: 1rem;
  text-decoration: none;
  font-weight: 600;
  color: #000000;
  font-size: 1.8rem;
  height: 83px;
}

.form-wrapper {
  background: #fff;
  padding: 42px 40px;
  max-width: 880px;
  width: 100%;
  box-shadow: 0px 3px 10px #1e497b33;
  border-radius: 20px;
}

.form-wrapper h2 {
  color: var(--textColor);
  font-weight: 600;
  font-size: 3.2rem;
  margin-bottom: 5px;
}

.form-wrapper .subheading {
  font-size: 1.8rem;
  font-weight: 500;
  margin-bottom: 30px;
  color: var(--black);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.form-group label,
.form-radio-grp legend {
  display: block;
  font-size: 1.6rem;
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--black);
}

.form-group label::after {
  content: "*";
  color: var(--red);
  font-size: 2rem;
}

.form-group-second label::after {
  content: " ";
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 15px;
  font-size: 1.6rem;
  border: 1px solid #d6d6d6;
  border-radius: 10px;
  outline: none;
  transition: 0.3s;
  color: var(--black);
  font-weight: 500;
  height: 58px;
}

.form-group select.wrong,
.form-group input.wrong {
  border: 1px solid var(--red);
}

.form-group input::placeholder,
.form-group select::placeholder {
  color: #636464;
  font-weight: 400;
}

.form-group select option[disabled] {
  color: #636464;
  font-weight: 400;
}

.form-group select {
  appearance: none;
  background: url("data:image/svg+xml,%3Csvg fill='red' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E")
    no-repeat right 10px center;
  background-size: 44px;
}

.radio-group {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-top: 10px;
}

.form-group .radio-option {
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid #ccc;
  padding: 0px 20px;
  border-radius: 10px;
  height: 58px;
  cursor: pointer;
  width: 141px;
}

.form-group .radio-option input[type="radio"] {
  accent-color: var(--red);
  transform: scale(0.5);
}

.form-footer {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-top: 40px;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-icons {
  display: flex;
}

.footer-icons > div {
  background: var(--white);
  border: 2px solid #e3edfd;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 59px;
  height: 59px;
}

.footer-icons > div:first-child {
  margin-right: -28px;
  z-index: 99;
}

.footer-icons > div:nth-child(2) {
  margin-right: -28px;
  z-index: 9;
}

.footer-icons img {
  width: 30px;
  height: 30px;
}

.submit-btn,
.next-btn,
.unlock-box button,
.otp-box button {
  background-color: var(--red);
  padding: 18px 42px;
  border: none;
  border-radius: 30px;
  font-weight: bold;
  cursor: pointer;
  transition: background 0.3s;
  color: var(--white);
  font-size: 1.6rem;
  align-items: center;
  display: flex;
  gap: 22px;
}

.submit-btn:hover,
.next-btn:hover,
.unlock-box button:hover,
.otp-box button:hover {
  background-color: #c62828;
}

.question-box .next-btn {
  background-color: #d6d6d6;
}

.question-box .next-btn.active {
  background-color: var(--red);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.question-header h3 {
  font-size: 2.2rem;
  font-weight: bold;
  color: var(--black);
  margin: 0;
}

.levels {
  display: flex;
  gap: 26px;
  align-items: center;
  font-size: 1.8rem;
  justify-content: end;
  font-weight: bold;
  color: #1e497b;
}

.levels label {
  display: flex;
  align-items: center;
  gap: 8px;
}

.levels input[type="radio"] {
  accent-color: var(--red);
  /* transform: scale(1.4); */
}

.question-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--black);
  margin-bottom: 16px;
}

.instruction {
  font-size: 1.6rem;
  font-weight: 400;
  color: #636464;
  margin-bottom: 20px;
}

.options {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: 1px solid #d6d6d6;
  border-radius: 10px;
  cursor: pointer;
  transition: background 0.2s, border-color 0.2s;
  color: var(--black);
  font-size: 1.6rem;
  font-weight: 600;
  gap: 20px;
}

.option.clicked {
  background-color: #e3edfd;
}

.option:hover {
  background-color: #f9f9f9;
}

.option.clicked:hover {
  background-color: #e3edfd;
}

.option input[type="radio"] {
  display: none;
}

.option-label {
  background-color: #e3edfd;
  color: var(--textColor);
  font-weight: bold;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1.2rem;
}

/* Selected state */
.option input[type="radio"]:checked + .option-label {
  background-color: var(--textColor);
  color: var(--white);
}

.levels input[type="radio"]:disabled {
  accent-color: var(--red);
}

.level input[type="radio"] {
  appearance: none;
  width: 25px;
  height: 25px;
  border: 2px solid #d6d6d6;
  border-radius: 50%;
  position: relative;
  cursor: default;
}

.level input[type="radio"]::before {
  content: "";
  width: 12.5px;
  height: 12.5px;
  background: transparent;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.level input[type="radio"]:checked::before {
  background: var(--red);
}

.level.active {
  color: var(--red);
}

.level.active input[type="radio"] {
  border-color: var(--red);
}

/* Completed level */
.level.completed {
  color: #1e3a8a;
  /* blue */
}

.level.completed input[type="radio"] {
  display: none;
}

.level.completed::before {
  content: "✔";
  color: white;
  background-color: #2e8b57;
  font-size: 12px;
  font-weight: bold;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.textarea-wrapper {
  background: #f6f6f6;
  border: 1px solid #d6d6d6;
  border-radius: 10px;
  padding: 20px;
  width: 100%;
  margin-top: 24px;
}

.textarea-wrapper textarea {
  width: 100%;
  height: 250px;
  border: none;
  background: transparent;
  resize: none;
  outline: none;
  font-size: 1.6rem;
  line-height: 1.5;
  color: var(--black);
}

.skip-next {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 22px;
  flex-wrap: wrap;
  gap: 15px;
}

.skip-btn {
  font-size: 1.6rem;
  color: var(--black);
  font-weight: 500;
  line-height: 30px;
  cursor: pointer;
  border: none;
  border-bottom: 1.4px solid var(--black);
  background: none;
}

.completion-box {
  padding: 156px 40px;
  text-align: center;
}

.completion-box img {
  width: 81px;
  margin-bottom: 36px;
}

.completion-box h2 {
  color: var(--textColor);
  font-weight: 600;
  font-size: 3.2rem;
  margin-bottom: 22px;
}

.completion-box p {
  font-size: 1.8rem;
  color: var(--black);
  font-weight: 500;
  line-height: 2.4rem;
}

.completion-box p span {
  display: block;
}

.completion-box .highlight {
  color: var(--red);
  font-weight: bold;
}

.unlock-box {
  padding: 60px 30px;
  text-align: center;
}

.unlock-box img {
  width: 296px;
  margin-bottom: 5px;
}

.unlock-box h3 {
  margin-top: 0;
  font-size: 3.2rem;
  color: var(--textColor);
  font-weight: 600;
  margin-bottom: 10px;
}

.unlock-box p {
  font-size: 1.8rem;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 25px;
}

.unlock-box input[type="tel"] {
  width: 100%;
  max-width: 384px;
  padding: 20px 16px;
  border: 1px solid #d6d6d6;
  border-radius: 10px;
  font-size: 2rem;
  font-weight: 500;
  height: 58px;
  color: var(--black);
  outline: none;
  margin-bottom: 25px;
}

.unlock-box form,
.otp-box form {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.unlock-label {
  font-size: 1.4rem;
  letter-spacing: 0.28rem;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 38px;
}

.otp-box {
  padding: 82px 30px;
  text-align: center;
}

.otp-box img {
  margin-bottom: 20px;
}

.otp-box h2 {
  margin-top: 0;
  font-size: 3.2rem;
  color: var(--textColor);
  font-weight: 600;
  margin-bottom: 10px;
}

.otp-box p {
  font-size: 1.8rem;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.otp-box .mobile-number {
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.otp-box .edit-icon {
  margin-left: 8px;
}

.otp-box .edit-icon img {
  margin: 0;
}

.otp-box input[type="text"] {
  width: 100%;
  max-width: 384px;
  padding: 20px 16px;
  border: 1px solid #d6d6d6;
  border-radius: 10px;
  font-size: 2rem;
  font-weight: 500;
  height: 58px;
  outline: none;
  color: var(--black);
}

.otp-box input[type="text"]::placeholder,
.unlock-box input[type="tel"]::placeholder {
  font-size: 1.6rem;
  font-weight: 400;
  color: #636464;
}

.otp-box .resend {
  margin-top: 32px;
  font-size: 1.4rem;
  color: var(--black);
  font-weight: 500;
}

.otp-box .timer {
  color: var(--red);
}

.resend-container {
  margin-top: 32px;
  display: none;
  justify-content: center;
}

.resend-container a {
  text-decoration: none;
  color: var(--red);
  font-weight: 500;
  font-size: 1.4rem;
  border-bottom: 1px solid;
  line-height: 2.4rem;
}

.otp-error-msg {
  color: var(--black);
  font-size: 1.4rem;
  font-weight: 600;
  position: absolute;
  right: 22px;
  top: 50%;
  transform: translateY(-50%);
}

.otp-error-msg img.error-icon {
  margin: 0;
}

.form-container {
  width: 384px;
  margin: 0 auto;
  position: relative;
  margin-bottom: 40px;
}

.form-radio-grp {
  margin-top: 30px;
  border: 0;
}

.assessment-wrapper {
  background: transparent linear-gradient(180deg, #ffffff 0%, #e3edfd 100%);
  height: auto;
}

.assessment-wrapper main {
  display: block;
  margin-top: 60px;
}

.assessment-container {
  width: 80%;
  background-color: var(--white);
  padding: 0 40px 40px;
  border-radius: 10px;
}

.tabs {
  display: flex;
  border-bottom: 2px solid #ddd;
}

.tab {
  padding: 23px;
  cursor: pointer;
  font-weight: 600;
  border-bottom: 3px solid transparent;
  font-size: 1.8rem;
}

.tab.active {
  border-color: var(--red);
  color: var(--red);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

#summary {
  padding-top: 36px;
}

.assessment-container #summary h2 {
  margin-top: 0;
  color: var(--black);
  font-size: 2.2rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.assessment-container #summary p {
  font-size: 1.6rem;
  color: var(--black);
  margin-bottom: 26px;
}

.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 40px;
}

.card {
  background-color: #eef2fc;
  padding: 30px 36px;
  border-radius: 10px;
  flex: 1 1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.card h4 {
  margin: 0 0 16px;
  font-size: 1.8rem;
  color: var(--black);
  font-weight: 600;
}

.card-container p {
  margin: 0;
}

.assessment-container .btn {
  margin-top: 22px;
  padding: 18px 46px;
  border: none;
  border-radius: 50px;
  font-weight: bold;
  color: var(--white);
  background-color: var(--red);
  cursor: pointer;
  font-size: 1.6rem;
  text-decoration: none;
  display: inline-block;
}

.assessment-container .btn:hover {
  opacity: 0.9;
}

.assessmet-question-header {
  display: flex;
  align-items: center;
  gap: 18px;
}

.assessmet-question-header h2 {
  font-size: 2.2rem;
  font-weight: bold;
  color: var(--black);
}

.badge-status {
  padding: 9px 32px;
  border-radius: 22px;
  font-size: 1.4rem;
  font-weight: 600;
}

.badge-status.incorrect {
  background: #f7e1df;
  color: #d53d3f;
}

.badge-status.correct {
  background: #ddf6eb;
  color: #219a58;
}

.badge-status.skipped {
  background: #f6f6f6;
  color: #636464;
}

.question-text {
  margin: 18px 0 28px;
  line-height: 1.5;
  font-size: 1.8rem;
  font-weight: 500;
  color: var(--black);
}

.options-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 22px;
}

.option-card {
  display: flex;
  align-items: center;
  padding: 18px 24px;
  border: 1px solid #d6d6d6;
  border-radius: 10px;
  background: var(--white);
  font-weight: 400;
  font-size: 1.6rem;
  transition: 0.3s;
}

.option-card.correct {
  background: #219a58;
  color: var(--white);
  border-color: #219a58;
  font-weight: bold;
}

.option-card.correct .option-label {
  background: white;
  color: #219a58;
}

.option-card.incorrect {
  background: var(--red);
  color: var(--white);
  border-color: var(--red);
  font-weight: bold;
}

.option-card.incorrect .option-label {
  background: var(--white);
  color: var(--red);
}

.explanation-box {
  background: #e3edfd;
  padding: 40px;
  border-radius: 10px;
  margin-top: 40px;
}

.explanation-box h4 {
  margin: 0 0 18px;
  color: var(--textColor);
  font-size: 2.2rem;
  font-weight: bold;
}

.explanation-box p {
  color: var(--black);
  font-size: 1.8rem;
}

.assessment-wrapper header {
  position: relative;
}

.assessment-output-box {
  border-bottom: 2px solid #ddd;
  padding: 36px 0 44px;
}

.yourcode-wrapper h6 {
  color: #636464;
  font-weight: 500;
  font-size: 1.6rem;
  margin-bottom: 20px;
}

.yourcode-wrapper .yourcode-box {
  background: #f6f6f6;
  border: 1px solid #d6d6d6;
  border-radius: 10px;
  font-size: 1.8rem;
  font-weight: 500;
  padding: 42px 26px;
  color: var(--black);
}

#performance .assessment-output-box:last-child {
  border: 0;
}

.assessment-content h1 {
  color: var(--textColor);
  font-size: 3.2rem;
  font-weight: bold;
}

.report-container {
  margin: 60px 0 46px;
  position: relative;
}

.report {
  position: absolute;
  bottom: 36px;
  left: 50%;
  transform: translate(-50%);
  text-align: center;
  width: 42%;
}

.report h3 {
  font-size: 8rem;
  font-weight: bold;
  color: #219a58;
}

.report h4 {
  font-size: 2.2rem;
  font-weight: 500;
  color: var(--black);
  margin: 18px 0 10px;
}

.report h5 {
  font-size: 1.8rem;
  font-weight: 500;
  color: var(--red);
}

.report-mobile {
  display: none;
}

.label-container {
  max-width: 320px;
  margin: 0 auto;
  flex-wrap: wrap;
  justify-content: space-around;
  gap: 42px;
  margin-bottom: 40px;
  display: none;
}

.label-box {
  border-radius: 16px;
  text-align: center;
}

.tag {
  display: inline-block;
  padding: 6px 14px;
  border-radius: 999px;
  font-weight: 600;
  font-size: 10px;
  margin-bottom: 6px;
  width: 110px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.range {
  font-size: 12px;
  font-weight: 500;
  color: var(--black);
}

/* Color Themes */
.curious {
  background-color: #f9efe3;
  color: #ff5e2d;
}

.ready {
  background-color: #f9f8d8;
  color: #ff9d2d;
}

.beginner {
  background-color: #ff9d2d;
  color: #d53d3f;
}

.pioneer {
  background-color: #ddf6eb;
  color: #219a58;
}

/* ===================== home page styling ======================*/

header.home-header {
  padding: 36px 0;
}

header.home-header > div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

header.home-header a img {
  width: 111px;
}

header.home-header nav {
  display: flex;
  gap: 56px;
  align-items: center;
}

header.home-header nav a {
  text-decoration: none;
  color: var(--black);
  font-weight: 500;
  font-size: 1.4rem;
}

main.hero-section {
  background: url(../img/top-bg.png) no-repeat 100% 18%;
  background-size: cover;
}

.inner-hero-section {
  display: flex;
  justify-content: space-between;
  padding: 48px 0 0;
}

.inner-hero-section h1 {
  font-size: 4.8rem;
  color: var(--black);
  font-weight: bold;
  line-height: 5.1rem;
  letter-spacing: -1.44px;
  padding-top: 28px;
}

.inner-hero-section h1 img {
  margin: 0 2px 0 16px;
  width: 44px;
}

.inner-hero-section h1 span {
  display: block;
}

.inner-hero-section p {
  font-size: 1.8rem;
  color: var(--black);
  line-height: 2.6rem;
  font-weight: 400;
  margin: 28px 0 34px;
}

.inner-hero-section p span {
  color: var(--red);
}

.line-br {
  display: block;
}

.ctabtn {
  background-color: var(--red);
  color: var(--white);
  font-weight: bold;
  text-transform: uppercase;
  padding: 18px 46px;
  border-radius: 100px;
  text-decoration: none;
  font-size: 1.6rem;
  display: inline-block;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.ctabtn:hover {
  transform: scale(1.05) translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.ctabtn:hover::before {
  left: 100%;
}

.free-assessment {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 34px;
}

.free-assessment span {
  color: #219a58;
  font-weight: 600;
  font-size: 2.2rem;
}

.free-assessment img {
  width: 50px;
}

.inner-hero-section .right img {
  width: 520px;
}

.assessment-section {
  padding: 80px 0 64px;
}

.section-title {
  font-size: 3.2rem;
  font-weight: 600;
  color: var(--textColor);
  margin-bottom: 40px;
  text-align: center;
}

.section-title.black {
  color: var(--black);
}

.section-title .text {
  color: #1e497b;
}

.assessment-section .cards-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
}

.assessment-section .info-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  flex: 1 1 calc(50% - 25px);
  padding: 40px 50px;
  transition: transform 0.3s;
  display: flex;
  align-items: center;
}

.frstColor{
    background-color: #FFFBE2;
}

.scndColor{
    background-color: #FFF3F4;
}

.thrdColor{
    background-color: #F5F8FF;
}

.frthColor{
    background-color: #FFF5EB;
}

.assessment-section .info-card  .info-content{
    width: calc(100% - 86px);
    padding-left: 32px;
}

.assessment-section .info-card:hover {
  transform: translateY(-6px);
}

.assessment-section h3 {
  font-size: 1.8rem;
  color: var(--black);
  font-weight: 600;
}

.assessment-section p {
  font-size: 1.6rem;
  color: #636464;
  font-weight: 400;
  margin-top: 12px;
}



.assess-your-skill-section {
  background-color: #f7faff;
  padding: 64px 0;
}

.card-icon-box {
  width: 86px;
  height: 86px;
  border-radius: 50%;
  padding: 21px;
}

.card-icon-box img {
  width: 100%;
}

.score-bg {
  padding-top: 12px;
}

.score-bg,
.cta-container {
  text-align: center;
}

.score-bg img {
  width: 874px;
}

.data-card {
  display: grid;
  grid-template-columns: auto auto;
  gap: 12px;
  width: 66%;
  margin: 50px auto;
}

.data-card > div {
  background: var(--white);
  box-shadow: 0px 3px 6px #1e497b0d;
  border-radius: 20px;
  text-align: center;
  padding: 50px 20px;
  transition: transform 0.3s;
}

.data-card > div:hover {
  transform: translateY(-6px);
}

.data-card h3 {
  font-size: 2.4rem;
  font-weight: bold;
  margin-bottom: 22px;
}

.data-card p {
  color: var(--black);
  font-size: 1.8rem;
  font-weight: 400;
}

.ai-beg {
  color: #d53d3f;
}

.ai-curious {
  color: #ff5e2d;
}

.ai-ready {
  color: #ff9d2d;
}

.ai-pioneer {
  color: #219a58;
}

.inner-invest-container {
  background-color: #fff5fc;
  border-radius: 20px;
  display: flex;
  justify-content: space-between;
  padding: 70px 70px 0;
  margin-top: 64px;
}

.inner-invest-container h4,
.pathway-section h4 {
  color: var(--black);
  font-size: 3.2rem;
  font-weight: 600;
}

.inner-invest-container p,
.pathway-section p {
  font-size: 1.8rem;
  font-weight: 400;
  color: var(--black);
  margin: 18px 0 32px;
}

.pathway-section p {
  margin: 34px 0 44px;
}

.inner-invest-container > div {
  flex: 1;
}

.inner-invest-container>div:first-child {
    padding-bottom: 30px;
}

.inner-invest-container .right,
.pathway-section .right {
  display: flex;
  justify-content: center;
}

.inner-invest-container img,
.pathway-section img {
  width: 86%;
}

.industry-experts-section {
  padding: 64px 0;
}

.journey-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 20px;
  flex-wrap: wrap;
  position: relative;
}

.journey-step {
  flex: 1 1 30%;
  min-width: 280px;
  padding: 20px;
}

.journey-step {
  text-align: center;
}

.icon-box {
  background: #e3edfd;
  box-shadow: 0px 3px 10px #e3edfd;
  border-radius: 20px;
  padding: 32px;
  width: 117px;
  height: 117px;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.icon-box img {
  width: 100%;
}

.journey-step h3 {
  font-size: 2.2rem;
  color: var(--black);
  font-weight: 600;
  margin: 36px 0 21px;
}

.journey-step p {
  font-size: 1.6rem;
  color: var(--black);
  font-weight: 400;
  line-height: 1.5;
}

/* Connector lines between steps */
.line-dot {
  flex: 0 0 20px;
  height: 2px;
  background-color: #d0d0d0;
  margin-top: 65px;
  position: relative;
}

.line-dot::before,
.line-dot::after {
  content: "";
  width: 10px;
  height: 10px;
  background-color: #d0d0d0;
  border-radius: 50%;
  position: absolute;
  top: -4px;
}

.line-dot::before {
  left: -5px;
}

.line-dot::after {
  right: -5px;
}

.pathway-section {
  background-color: #f7f6ff;
  padding: 70px;
}

.inner-pathway-container {
  display: flex;
  justify-content: space-between;
}

.inner-pathway-container > div {
  flex: 1;
}

.program-designed-section {
  background-color: #fff0dd;
}

.program-designed-section .inner-pathway-container {
  flex-direction: row-reverse;
}

.program-designed-section .inner-pathway-container .right {
  justify-content: flex-start;
}

.program-designed-section .inner-pathway-container .left {
  text-align: right;
}

.testimonial-section {
  padding: 64px 0;
  background-color: #f7f7f7;
}

.testimonial-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 40px;
}

/* Left Content */
.testimonial-left {
  flex: 1 1 40%;
  min-width: 300px;
}

.testimonial-left h2,
.partner-left h2 {
  text-align: left;
  margin: 0;
  line-height: 3.8rem;
}

.testimonial-left p,
.partner-left p {
  font-size: 1.8rem;
  color: var(--black);
  font-weight: 400;
  margin: 20px 0 36px;
}

/* Right Testimonial */
.testimonial-right {
  flex: 1 1 55%;
  min-width: 280px;
}

.testimonial-card {
  background: #fff;
  padding: 40px;
  border-radius: 20px;
  /* box-shadow: 0px 0px 20px #1E497B2B; */
  position: relative;
}

.testimonial-text {
  font-size: 2rem;
  color: var(--black);
  font-weight: 600;
  margin-bottom: 30px;
  line-height: 2.6rem;
}

.testimonial-user {
  display: flex;
  align-items: center;
  gap: 14px;
}

.owl-carousel .owl-item .testimonial-user img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.testimonial-user strong {
  font-size: 1.8rem;
  color: var(--black);
  font-weight: 600;
  line-height: 2.8rem;
}

.testimonial-user span {
  color: #636464;
  font-size: 1.4rem;
  font-weight: 500;
}

.testimonial-slider .owl-dots {
  padding: 20px 0 5px;
}

.owl-dots {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  gap: 6px;
}

.owl-dot {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background-color: #d6d6d6 !important;
  transition: all 0.3s ease;
}

.owl-dot.active {
  background-color: #e01f26 !important;
  transform: scale(1.2);
}

.testimonial-slider {
  position: relative;
  overflow: hidden;
}

.partner-section {
  padding: 64px 0;
}

.partner-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  gap: 40px;
}

/* Left Content */
.partner-left {
  flex: 1 1 40%;
  min-width: 300px;
}

.partner-left p strong {
  font-weight: 600;
}

/* Right Logos */
.partner-right {
  flex: 1 1 50%;
  display: flex;
  justify-content: center;
}

.logo-grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(100px, 200px));
  gap: 30px 55px;
  align-items: center;
  justify-items: center;
}

.logo-grid img {
  max-width: 219px;
  filter: grayscale(0%);
  transition: filter 0.3s;
}

.logo-grid img:hover {
  filter: grayscale(50%);
}

.site-footer {
  background-color: var(--white);
  font-size: 1.2rem;
  color: var(--black);
  margin-bottom: 120px;
}

.footer-container {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 26px;
  text-align: center;
  /* border-top: 1px solid #ddd; */
  padding: 30px 0;
}

.footer-container p,
.footer-container a {
  margin: 0;
  color: var(--black);
  text-decoration: none;
  font-weight: 400;
}

.footer-container a:hover {
  text-decoration: underline;
}

.divider {
  width: 1px;
  height: 16px;
  background-color: #ccc;
}

.banner {
  padding: 4px 0;
  background: #1e497b;
  box-shadow: 0px -4px 10px #00000005;
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 99;
}

.inner-banner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 60px;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info img {
  width: 58px;
  margin-right: 15px;
}

.user-text p {
  color: #e3edfd;
  font-weight: 500;
  font-size: 1.6rem;
}

.user-text .name {
  font-size: 1.8rem;
  color: var(--white);
  font-weight: bold;
  margin-bottom: 4px;
}

.middle-text {
  font-weight: bold;
  font-size: 2rem;
  color: var(--white);
  display: flex;
  align-items: center;
  gap: 15px;
  /* margin-left: auto; */
  /* margin-right: 90px; */
}

.middle-text img {
  width: 24px;
}

.cta-btn {
  display: flex;
  align-items: center;
}

.free-badge {
  background: url(../img/free-bg.png) no-repeat center;
  color: var(--white);
  font-weight: bold;
  font-size: 1.6rem;
  margin-right: -25px;
  z-index: 1;
  background-size: cover;
  width: 84px;
  height: 84px;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: rotateBox 2s linear infinite;
  position: relative;
}

.free-badge span {
  transform: rotate(-360deg);
  animation: counterRotate 2s linear infinite;
  display: inline-block;
}

@keyframes rotateBox {
  from {
    transform: rotate(-15deg);
  }

  to {
    transform: rotate(345deg);
  }
}

@keyframes counterRotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(-360deg);
  }
}

.assess-your-skill-section .highlight {
  position: relative;
  display: inline-block;
}

.assess-your-skill-section .highlight::after {
  content: "";
  position: absolute;
  left: -16px;
  bottom: -5px;
  width: 123%;
  height: 8px;
  background: url(../img/line.png) no-repeat;
  background-size: cover;
  pointer-events: none;
}

.mobileView {
  display: none;
}

.owl-theme .owl-dots .owl-dot span {
  display: none;
}

.ctaContainer {
  text-align: center;
  width: 100%;
  padding: 26px 0px 18px;
}

.space-1 {
  padding: 5px 0 10px;
}

.space-2 {
  padding: 5px 0 20px;
}

.mob-data-container {
  display: none;
}

.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loader-container.active {
  display: flex;
}

.loader {
  border: 8px solid #f3f3f3;
  border-top: 8px solid var(--red);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.questions-loader {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

#toast {
  position: fixed;
  top: -100px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: white;
  padding: 15px 30px;
  border-radius: 5px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: top 0.5s ease, opacity 0.5s ease;
  z-index: 9999;
  font-weight: 500;
  font-size: 1.4rem;
}

#toast span {
  border-bottom: 1px solid #ffff00;
}

#toast.show {
  top: 20px;
  opacity: 1;
}

.form-wrapper .form-group {
  position: relative;
}

.err-msg {
  position: absolute;
  bottom: -18px;
  font-size: 12px;
  color: var(--red);
  font-weight: 400;
  display: none;
}

.err-msg.show {
  display: block;
}

ul.report_list {
  font-size: 15px;
  margin: 2%;
}

.wrapper .loader-wrap {
  display: none;
}

.wrapper .loader-wrap.active {
  display: block;
}

.wrapper .main-question-screen.hide {
  display: none;
}

.retrieve-container .ctabtn {
  margin-top: 25px;
}

.wrapper .retrieve-container {
  display: none;
}

.wrapper .retrieve-container.active {
  display: block;
}

.hide {
  display: none;
}

.levels-sec {
  display: flex;
  align-items: center;
  gap: 56px;
}

.top-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.top-header-content .alarm-sec {
  background: var(--white);
  border: 0.5px solid #1e497b;
  color: #1e497b;
  font-weight: bold;
  font-size: 29px;
  display: flex;
  align-items: center;
  padding: 8px 30px;
  border-radius: 40px;
  /* font-family: 'DS-Digital', sans-serif; */
  font-family: "DSDIGI";
  gap: 9px;
  width: 155px;
  height: 46px;
}

.top-header-content .alarm-sec img {
  width: 22px;
}

.question-screen-container header {
  position: relative;
  display: flex;
  top: unset;
  left: unset;
  transform: unset;
}

.question-screen-container main {
  display: block;
  margin-top: 34px;
}

.options-wrapper .option-card {
  gap: 12px;
}
