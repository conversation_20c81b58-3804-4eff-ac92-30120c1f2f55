<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <title>Your Assessment Report | TechGIG</title>

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Mona+Sans:wght@400;600;700&display=swap" rel="stylesheet" />

    <!-- External CSS -->
    <link rel="stylesheet" href="./css/style.css" />
    <link rel="stylesheet" href="./css/responsive.css">


    <!-- Favicon -->
    <link rel="icon" href="./img/favicon.ico" type="image/x-icon" />
</head>

<body>
    <section class="wrapper assessment-wrapper h-none">
        <div class="wrp_container">

            <!-- Header -->
            <header>
                <a href="/" class="tg_logo" aria-label="TechGIG Home">
                    <img src="./img/TechGigLogo.png" alt="TechGIG logo" loading="lazy" />
                </a>
            </header>

            <main role="main" class="assessment-content">

                <h1>Your Assessment Report</h1>
                <div class="report-container">
                    <img src="./img/report-bg.png" class="report-desktop" alt="">
                    <img src="./img/report-bg-mobile.png" class="report-mobile" alt="">
                    <div class="report">
                        <h3>18</h3>
                        <h4>You scored <strong><span>18/20</span> points</strong></h4>
                        <h5>25% devs scored 20/20</h5>
                    </div>
                </div>
                <div class="label-container">
                    <div class="label-box">
                        <div class="tag curious">AI Curious</div>
                        <div class="range">6 – 10</div>
                    </div>
                    <div class="label-box">
                        <div class="tag ready">AI Ready</div>
                        <div class="range">11 – 15</div>
                    </div>
                    <div class="label-box">
                        <div class="tag beginner">AI Beginner</div>
                        <div class="range">0 – 5</div>
                    </div>
                    <div class="label-box">
                        <div class="tag pioneer">AI Pioneer</div>
                        <div class="range">16 – 20</div>
                    </div>
                </div>
                <div class="assessment-container">

                    <!-- Tabs -->
                    <div class="tabs">
                        <div class="tab active" data-tab="summary">Assessment Summary</div>
                        <div class="tab" data-tab="performance">Your Performance</div>
                    </div>

                    <!-- Tab Content 1 -->
                    <div id="summary" class="tab-content active">
                        <h2>Your Performance Summary</h2>
                        <p>
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam magna erat, convallis a
                            dignissim quis, finibus vel est. Nam feugiat lobortis est eu bibendum. Nam hendrerit turpis
                            ut viverra congue. Morbi tristique vestibulum nisi. Nulla pellentesque gravida turpis sit
                            amet semper. Cras in est nisi. Praesent hendrerit risus arcu, a laoreet est porta sed. Etiam
                            quis vulputate nunc. Cras dapibus suscipit nisl id eleifend.
                        </p>

                        <p>
                            Duis convallis convallis
                            laoreet. Sed et velit in turpis hendrerit vestibulum. Pellentesque a lobortis tellus. Mauris
                            sit amet congue purus. Maecenas pellentesque augue a consequat dapibus. Nulla ultrices
                            blandit purus eu faucibus. Proin maximus odio leo, quis suscipit tortor dapibus id.
                            Vestibulum sit amet bibendum neque, a elementum elit. Orci varius natoque penatibus et
                            magnis dis parturient montes, nascetur ridiculus mus. Nulla tempus tempus elit sit amet
                            interdum. Etiam efficitur ultrices ex, ac gravida sapien luctus at. Class aptent taciti
                            sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos.
                        </p>

                        <h2>Recommended Learning Path</h2>
                        <p>
                            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam magna erat, convallis a
                            dignissim quis, finibus vel est. Nam feugiat lobortis est eu bibendum. Nam hendrerit turpis
                            ut viverra congue. Morbi tristique vestibulum nisi. Nulla pellentesque gravida turpis sit
                            amet semper. Cras in est nisi. Praesent hendrerit risus arcu, a laoreet est porta sed. Etiam
                            quis vulputate nunc. Cras dapibus suscipit nisl id eleifend.
                        </p>

                        <p>
                            Duis convallis convallis
                            laoreet. Sed et velit in turpis hendrerit vestibulum. Pellentesque a lobortis tellus. Mauris
                            sit amet congue purus. Maecenas pellentesque augue a consequat dapibus. Nulla ultrices
                            blandit purus eu faucibus. Proin maximus odio leo, quis suscipit tortor dapibus id.
                            Vestibulum sit amet bibendum neque, a elementum elit. Orci varius natoque penatibus et
                            magnis dis parturient montes, nascetur ridiculus mus. Nulla tempus tempus elit sit amet
                            interdum. Etiam efficitur ultrices ex, ac gravida sapien luctus at. Class aptent taciti
                            sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos.
                        </p>

                        <div class="card-container">
                            <div class="card">
                                <h4>Lorem Ipsum</h4>
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam magna erat, convallis
                                    a dignissim quis,</p>
                                <a href="" class="btn">RETAKE ASSESSMENT</a>
                            </div>
                            <div class="card">
                                <h4>Lorem Ipsum</h4>
                                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam magna erat, convallis
                                    a dignissim quis,</p>
                                <a href="" class="btn">CREATE RESUME</a>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Content 2 -->
                    <div id="performance" class="tab-content">
                        <div class="assessment-output-box">
                            <div class="assessmet-question-header">
                                <h2>Question 1/20:</h2>
                                <span class="badge-status incorrect">Incorrect</span>
                            </div>

                            <p class="question-text">
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque ornare, ante vel
                                vehicula
                                rutrum, magna nulla aliquam nisl,
                                sagittis vulputate massa odio eu nunc. Nullam laoreet augue sapien, eu facilisis turpis
                                tincidunt in. Nunc consectetur urna
                                a leo congue efficitur. Proin interdum ante a velit suscipit dictum. Vivamus id egestas
                                eros, quis pharetra nulla.
                            </p>

                            <div class="options-wrapper">
                                <div class="option-card">
                                    <div class="option-label">A</div>
                                    Lorem Ipsum
                                </div>

                                <div class="option-card correct">
                                    <div class="option-label">B</div>
                                    Lorem Ipsum
                                </div>

                                <div class="option-card">
                                    <div class="option-label">C</div>
                                    Lorem Ipsum
                                </div>

                                <div class="option-card incorrect">
                                    <div class="option-label">D</div>
                                    Lorem Ipsum
                                </div>
                            </div>

                            <div class="explanation-box">
                                <h4>Reasoning:</h4>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque ornare, ante vel
                                    vehicula rutrum, magna nulla aliquam
                                    nisl, sagittis vulputate massa odio eu nunc. Nullam laoreet augue sapien, eu
                                    facilisis
                                    turpis tincidunt in. Nunc
                                    consectetur urna a leo congue efficitur. Proin interdum ante a velit suscipit
                                    dictum.
                                    Vivamus id egestas eros, quis
                                    pharetra nulla.
                                </p>
                            </div>
                        </div>
                        <div class="assessment-output-box">
                            <div class="assessmet-question-header">
                                <h2>Question 1/20:</h2>
                                <span class="badge-status correct">Correct</span>
                            </div>

                            <p class="question-text">
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque ornare, ante vel
                                vehicula
                                rutrum, magna nulla aliquam nisl,
                                sagittis vulputate massa odio eu nunc. Nullam laoreet augue sapien, eu facilisis turpis
                                tincidunt in. Nunc consectetur urna
                                a leo congue efficitur. Proin interdum ante a velit suscipit dictum. Vivamus id egestas
                                eros, quis pharetra nulla.
                            </p>

                            <div class="yourcode-wrapper">
                                <h6>
                                    Your Code
                                </h6>
                                <div class="yourcode-box">
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque ornare, ante vel
                                    vehicula rutrum, magna nulla aliquam nisl, sagittis vulputate massa odio eu nunc.
                                    Nullam laoreet augue sapien, eu facilisis turpis tincidunt in. Nunc consectetur urna
                                    a leo congue efficitur. Proin interdum ante a velit suscipit dictum. Vivamus id
                                    egestas eros, quis pharetra nulla.
                                </div>
                            </div>

                            <div class="explanation-box">
                                <h4>Reasoning:</h4>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque ornare, ante vel
                                    vehicula rutrum, magna nulla aliquam
                                    nisl, sagittis vulputate massa odio eu nunc. Nullam laoreet augue sapien, eu
                                    facilisis
                                    turpis tincidunt in. Nunc
                                    consectetur urna a leo congue efficitur. Proin interdum ante a velit suscipit
                                    dictum.
                                    Vivamus id egestas eros, quis
                                    pharetra nulla.
                                </p>
                            </div>
                        </div>
                        <div class="assessment-output-box">
                            <div class="assessmet-question-header">
                                <h2>Question 1/20:</h2>
                                <span class="badge-status skipped">Skipped</span>
                            </div>

                            <p class="question-text">
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque ornare, ante vel
                                vehicula
                                rutrum, magna nulla aliquam nisl,
                                sagittis vulputate massa odio eu nunc. Nullam laoreet augue sapien, eu facilisis turpis
                                tincidunt in. Nunc consectetur urna
                                a leo congue efficitur. Proin interdum ante a velit suscipit dictum. Vivamus id egestas
                                eros, quis pharetra nulla.
                            </p>

                            <div class="options-wrapper">
                                <div class="option-card">
                                    <div class="option-label">A</div>
                                    Lorem Ipsum
                                </div>

                                <div class="option-card correct">
                                    <div class="option-label">B</div>
                                    Lorem Ipsum
                                </div>

                                <div class="option-card">
                                    <div class="option-label">C</div>
                                    Lorem Ipsum
                                </div>

                                <div class="option-card">
                                    <div class="option-label">D</div>
                                    Lorem Ipsum
                                </div>
                            </div>

                            <div class="explanation-box">
                                <h4>Reasoning:</h4>
                                <p>
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque ornare, ante vel
                                    vehicula rutrum, magna nulla aliquam
                                    nisl, sagittis vulputate massa odio eu nunc. Nullam laoreet augue sapien, eu
                                    facilisis
                                    turpis tincidunt in. Nunc
                                    consectetur urna a leo congue efficitur. Proin interdum ante a velit suscipit
                                    dictum.
                                    Vivamus id egestas eros, quis
                                    pharetra nulla.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </section>

    <script>
        const tabs = document.querySelectorAll('.tab');
        const contents = document.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active from all tabs & contents
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));

                // Activate clicked tab & related content
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab).classList.add('active');
            });
        });
    </script>
</body>

</html>