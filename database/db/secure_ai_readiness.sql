-- Adminer 4.6.3 MySQL dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

DROP TABLE IF EXISTS `access_tokens`;
CREATE TABLE `access_tokens` (
  `access_token` varchar(255) NOT NULL,
  `user_id` int(10) unsigned NOT NULL,
  `expiry_date` varchar(255) NOT NULL,
  KEY `user_id` (`user_id`),
  KEY `access_token` (`access_token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `usergroup_id` int(11) NOT NULL DEFAULT '3',
  `remember_token` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admins_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

INSERT INTO `admins` (`id`, `name`, `email`, `password`, `usergroup_id`, `remember_token`, `created_at`, `updated_at`) VALUES
(1,	'Raj Kamal singh',	'<EMAIL>',	'$2y$10$iQxx9vbAWSedJCKEEUHaRuL0PpLzhiAPFXtLW7KdER6WCkYNXINyO',	1,	'1ILgNyC7ZXrWQnRYDUXjw3oZiETsFyST5hpHx5CTaZKKFYq9cQ9yhMdOijvX',	'2016-11-01 04:54:49',	'2020-10-07 06:10:26'),
(2,	'Raj Kamal',	'<EMAIL>',	'$2y$10$snF4asguDfo50mj6Eam.7.cqHEDngVsuTsW7RmVkCDS5QEVwX7MUC',	1,	'MoVNVQZ2FlXC2T5QqaywOb2hazE14MqYH3wWWb8KlngjOOqgNt5DR3YSoIAI',	'2016-11-02 11:05:05',	'2017-11-21 10:09:56'),
(3,	'Ashutosh Sharma',	'<EMAIL>',	'$2y$10$tehqdK/xEbJJcdb.BnqoLuVi2GwCYMbFPZh5bdvWFMsDZ01BVmFcm',	1,	'',	'2020-12-16 18:39:01',	'2020-12-16 18:39:01'),
(4,	'Anup Kumar',	'<EMAIL>',	'$2y$10$5ejlSBkqjg6cT67MSwc..epykPIisASGIRE1QGvmAlMJSJu53Rh1S',	1,	'',	'2020-12-16 18:39:01',	'2020-12-16 18:39:01');

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `jobroles`;
CREATE TABLE `jobroles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(255) DEFAULT NULL,
  `role_category` varchar(255) DEFAULT NULL,
  `status` enum('Active','Inactive') DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=MRG_MyISAM DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `jobrole_prompts`;
CREATE TABLE `jobrole_prompts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `jobrole_id` int(11) DEFAULT NULL,
  `exp_min_in_year` int(11) DEFAULT NULL,
  `exp_max_in_year` int(11) DEFAULT NULL,
  `prompt1` text,
  `prompt2` text,
  `prompt3` text,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MRG_MyISAM DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `rk_menu_categories`;
CREATE TABLE `rk_menu_categories` (
  `menu_category_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `category_menu_link` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `status` enum('Active','Inactive') NOT NULL,
  `position` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`menu_category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

INSERT INTO `rk_menu_categories` (`menu_category_id`, `name`, `category_menu_link`, `description`, `status`, `position`, `created_at`, `updated_at`) VALUES
(2,	'Role Management',	'#',	'All user management go inside it',	'Active',	4,	'2016-11-04 15:39:19',	'2016-11-04 15:39:19'),
(5,	'Settings',	'#',	'System settings',	'Active',	5,	'2017-07-16 16:37:05',	'2017-07-16 16:37:05'),
(9,	'User management',	'#',	'<p>All user related activity </p>',	'Active',	2,	'2019-07-13 10:14:02',	'2020-11-27 08:08:06');

DROP TABLE IF EXISTS `rk_modules`;
CREATE TABLE `rk_modules` (
  `module_id` int(11) NOT NULL AUTO_INCREMENT,
  `module_type` varchar(255) CHARACTER SET utf8 NOT NULL,
  `module_name` varchar(255) CHARACTER SET utf8 NOT NULL,
  `descriptions` text CHARACTER SET utf8 NOT NULL,
  `mapping_url` varchar(255) CHARACTER SET utf8 NOT NULL,
  `menu_type` int(11) NOT NULL DEFAULT '0',
  `menu_name` varchar(255) CHARACTER SET utf8 DEFAULT '',
  `priority` int(11) NOT NULL DEFAULT '0',
  `status` enum('Active','Inactive') CHARACTER SET utf8 NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`module_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

INSERT INTO `rk_modules` (`module_id`, `module_type`, `module_name`, `descriptions`, `mapping_url`, `menu_type`, `menu_name`, `priority`, `status`, `created_at`, `updated_at`) VALUES
(1,	'rbac',	'Manage Usergroup',	'This module will help to manage usergroup',	'usergroup',	2,	'Manage Usergroup',	0,	'Active',	'2016-11-04 15:39:54',	'2016-11-04 21:26:27'),
(2,	'rbac',	'Manage Modules',	'List down all modules which is registered',	'module',	2,	'Manage Module & Associations',	1,	'Active',	'2016-11-04 15:41:28',	'2016-11-05 05:44:25'),
(4,	'rbac',	'Manage Admin Users',	'Manage admin users - can add/edit delete data',	'adminuser',	2,	'Manage Admins',	0,	'Active',	'2016-11-05 08:52:54',	'2016-11-04 21:52:54'),
(5,	'rbac',	'Assign Modules',	'assign modules to usergroup & user',	'usergroup/{id}/assign',	2,	'',	0,	'Active',	'2016-11-05 11:21:19',	'2016-11-05 07:17:57'),
(6,	'rbac',	'Update Admin Profile',	'<p>User can update own profile using this</p>\r\n',	'settings',	5,	'Manage Profile',	0,	'Active',	'2016-11-05 14:52:21',	'2019-07-13 10:16:26'),
(8,	'cms',	'Manage Games',	'<p>Manage Games</p>',	'games',	1,	'Manage Games',	0,	'Active',	'2017-07-16 11:08:40',	'2020-11-27 02:34:26'),
(9,	'cms',	'Manage Contests',	'<p>Manage Contests</p>',	'contests',	1,	'Manage Contests',	1,	'Active',	'2017-07-16 11:09:13',	'2020-11-27 02:34:56'),
(10,	'cms',	'Manage Domain/Partners',	'<p>Manage Partners</p>',	'partners',	8,	'Manage Partners',	2,	'Active',	'2017-07-16 11:10:05',	'2020-12-10 06:18:38'),
(11,	'cms',	'Manage Publishers',	'<p>Manage Publishers</p>',	'publishers',	8,	'Manage Publishers',	3,	'Active',	'2017-07-16 11:10:46',	'2020-12-10 06:17:46'),
(12,	'cms',	'Manage Pages/Block',	'<p>to manage pages and blocks to display ads configuration</p>',	'pages',	8,	'Manage Pages/Block',	4,	'Active',	'2017-07-16 11:12:04',	'2020-12-10 06:19:21'),
(13,	'cms',	'Manage Articles',	'<p>Active language will be supported by this site</p>\r\n',	'articles',	1,	'Manage Articles',	1,	'Active',	'2017-07-16 11:21:46',	'2019-06-30 03:48:31'),
(17,	'cms',	'Manage Site Configuration',	'<p>Manage Siteconfig</p>\r\n',	'siteconfig',	5,	'Manage Site Configuration',	0,	'Active',	'2019-07-13 17:29:05',	'2019-07-13 17:29:05'),
(18,	'cms',	'Page Ads Config',	'<p>Page Ads Config</p>',	'adsconfig',	8,	'Page Ads Config',	5,	'Active',	'2020-12-10 12:29:15',	'2020-12-10 06:59:15'),
(19,	'cms',	'Manage Users',	'<p>Manage all platform users</p>',	'users',	9,	'Manage Users',	0,	'Active',	'2020-12-10 12:35:55',	'2020-12-10 07:05:55'),
(20,	'cms',	'Manage Winners',	'<p>Manage Winners</p>',	'winners',	9,	'Manage Winners',	1,	'Active',	'2020-12-10 12:36:39',	'2020-12-10 07:06:39'),
(21,	'rbac',	'Internal Reporting',	'<p>Internal Reporting</p>',	'internal_reporting',	7,	'Internal Reporting',	0,	'Inactive',	'2020-12-31 12:31:57',	'2021-04-30 12:10:32'),
(22,	'cms',	'Ad Tracker',	'<p>Ad Tracker</p>',	'adstracker',	7,	'Ad Tracker',	0,	'Active',	'2021-02-05 01:16:57',	'2021-05-01 12:53:55'),
(23,	'cms',	'AdTracker by Domain',	'<p>Ad Click count group by domain</p>',	'adstracker_report',	7,	'AdTracker by Domain',	0,	'Active',	'2021-05-23 23:34:39',	'2021-05-23 18:04:39'),
(24,	'cms',	'Registration Metrics',	'<p>User registration metrics</p>',	'reg_metrics',	7,	'Registration Metrics',	0,	'Active',	'2021-05-24 01:14:20',	'2021-05-23 19:44:20'),
(25,	'cms',	'All Game Metrics',	'<p>All game metrics</p>',	'all_game_metrics',	7,	'All Game Metrics',	0,	'Active',	'2021-05-24 09:50:48',	'2021-05-24 04:20:48'),
(26,	'cms',	'Each Game Metrics',	'<p>Each Game Metrics</p>',	'each_game_metrics',	7,	'Each Game Metrics',	0,	'Active',	'2021-05-24 16:10:00',	'2021-05-24 10:41:30'),
(27,	'CMS',	'Adsterra Page Ads Config',	'<p>Adsterra Page Ads Config</p>',	'adsterraadsconfig',	8,	'Adsterra Page Ads Config',	5,	'Active',	'2024-06-07 13:09:43',	'2024-06-07 07:39:43');

DROP TABLE IF EXISTS `rk_module_activity_links`;
CREATE TABLE `rk_module_activity_links` (
  `activity_id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_id` int(11) NOT NULL,
  `module_id` int(11) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `rk_usergroups`;
CREATE TABLE `rk_usergroups` (
  `usergroup_id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `lang_code` char(2) CHARACTER SET utf8 NOT NULL DEFAULT 'EN',
  `name` varchar(255) CHARACTER SET utf8 NOT NULL,
  `user_type` varchar(255) CHARACTER SET utf8 NOT NULL,
  `status` enum('Active','Inactive') CHARACTER SET utf8 NOT NULL,
  `parent_usergroup` mediumint(9) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`usergroup_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

INSERT INTO `rk_usergroups` (`usergroup_id`, `lang_code`, `name`, `user_type`, `status`, `parent_usergroup`, `created_at`, `updated_at`) VALUES
(1,	'EN',	'Super Administrator',	'A',	'Active',	0,	'2016-11-04 15:07:23',	'2016-11-04 04:21:14'),
(2,	'EN',	'Administrator',	'A',	'Active',	0,	'2016-11-04 15:07:50',	'2016-11-04 04:07:50');

DROP TABLE IF EXISTS `rk_usergroup_privilege`;
CREATE TABLE `rk_usergroup_privilege` (
  `privilege_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL DEFAULT '0',
  `usergroup_id` mediumint(8) unsigned NOT NULL,
  `module_id` mediumint(8) unsigned NOT NULL,
  `is_access` enum('Y','N') NOT NULL,
  `is_write` enum('Y','N') CHARACTER SET utf8 NOT NULL,
  `status` enum('Active','Inactive') CHARACTER SET utf8 NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`privilege_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

INSERT INTO `rk_usergroup_privilege` (`privilege_id`, `user_id`, `usergroup_id`, `module_id`, `is_access`, `is_write`, `status`, `created_at`, `updated_at`) VALUES
(156,	0,	2,	8,	'Y',	'Y',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(157,	0,	2,	9,	'N',	'N',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(158,	0,	2,	10,	'Y',	'Y',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(159,	0,	2,	11,	'Y',	'Y',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(160,	0,	2,	12,	'Y',	'Y',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(161,	0,	2,	13,	'Y',	'Y',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(162,	0,	2,	1,	'N',	'Y',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(163,	0,	2,	2,	'N',	'N',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(164,	0,	2,	3,	'N',	'N',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(165,	0,	2,	4,	'N',	'N',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(166,	0,	2,	5,	'N',	'N',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(167,	0,	2,	6,	'Y',	'Y',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(168,	0,	2,	7,	'N',	'N',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(169,	0,	2,	17,	'N',	'N',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(170,	0,	2,	16,	'N',	'N',	'Active',	'2019-07-30 15:31:54',	'2019-07-30 15:31:54'),
(445,	0,	1,	1,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(446,	0,	1,	2,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(447,	0,	1,	4,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(448,	0,	1,	5,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(449,	0,	1,	6,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(450,	0,	1,	17,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(451,	0,	1,	8,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(452,	0,	1,	9,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(453,	0,	1,	13,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(454,	0,	1,	10,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(455,	0,	1,	11,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(456,	0,	1,	12,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(457,	0,	1,	18,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(458,	0,	1,	19,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(459,	0,	1,	20,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(460,	0,	1,	22,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(461,	0,	1,	23,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(462,	0,	1,	24,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(463,	0,	1,	25,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(464,	0,	1,	26,	'Y',	'Y',	'Inactive',	'2021-05-24 16:10:07',	'2021-05-24 16:10:07'),
(465,	0,	1,	1,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(466,	0,	1,	2,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(467,	0,	1,	4,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(468,	0,	1,	5,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(469,	0,	1,	6,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(470,	0,	1,	17,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(471,	0,	1,	8,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(472,	0,	1,	9,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(473,	0,	1,	13,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(474,	0,	1,	10,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(475,	0,	1,	11,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(476,	0,	1,	12,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(477,	0,	1,	18,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(478,	0,	1,	27,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(479,	0,	1,	19,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(480,	0,	1,	20,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(481,	0,	1,	22,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(482,	0,	1,	23,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(483,	0,	1,	24,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(484,	0,	1,	25,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55'),
(485,	0,	1,	26,	'Y',	'Y',	'Active',	'2024-06-07 13:09:55',	'2024-06-07 13:09:55');

DROP TABLE IF EXISTS `token_blocklist`;
CREATE TABLE `token_blocklist` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `jti` varchar(36) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `jti` (`jti`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `email` varchar(120) NOT NULL,
  `password` varchar(128) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `user_assessments`;
CREATE TABLE `user_assessments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_profile_id` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `user_assessment_questions`;
CREATE TABLE `user_assessment_questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_assessment_id` int(11) DEFAULT NULL,
  `question` json DEFAULT NULL,
  `answer` text,
  `level` enum('L1','L2','L3') DEFAULT 'L1',
  `token` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_assessment_id` (`user_assessment_id`),
  CONSTRAINT `user_assessment_questions_ibfk_1` FOREIGN KEY (`user_assessment_id`) REFERENCES `user_assessments` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `user_assessment_reports`;
CREATE TABLE `user_assessment_reports` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_assessment_id` int(11) DEFAULT NULL,
  `answer` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


DROP TABLE IF EXISTS `user_profiles`;
CREATE TABLE `user_profiles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `job role_id` int(11) DEFAULT NULL,
  `exp_in_year` int(11) DEFAULT NULL,
  `is_ai_used` enum('Yes','No') NOT NULL DEFAULT 'No',
  `mobile_number` varchar(15) DEFAULT NULL,
  `current_company` varchar(255) DEFAULT NULL,
  `current_role` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- 2025-06-30 18:13:37
