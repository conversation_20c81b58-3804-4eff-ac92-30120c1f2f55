<?php


//Admin Login
Route::get('login', '<PERSON><PERSON>A<PERSON>\LoginController@showLoginForm');
Route::post('login', '<PERSON><PERSON><PERSON><PERSON>\LoginController@login');
Route::get('logout', '<PERSON><PERSON><PERSON><PERSON>\LoginController@logout');

//Admin Register
// Route::get('register', 'AdminAuth\RegisterController@showRegistrationForm');
// Route::post('register', 'AdminAuth\RegisterController@register');

//Admin Passwords
Route::post('password/email', 'AdminAuth\ForgotPasswordController@sendResetLinkEmail');
Route::post('password/reset', 'AdminAuth\ResetPasswordController@reset');
Route::get('password/reset', 'AdminAuth\ForgotPasswordController@showLinkRequestForm');
Route::get('password/reset/{token}', 'AdminAuth\ResetPasswordController@showResetForm');

Route::get('2FA', 'AdminAuth\TwoFAController@showOTPForm');
Route::post('2FA', 'AdminAuth\TwoFAController@submitOTP');

Route::get('testemail', 'AdminAuth\CmsController@testEmail');