<?php
if (!defined('API_SERVER_URL')) 
{define('API_SERVER_URL',env('API_SERVER_URL','http://localhost:5000/api'));}

return [
'api_url' => env('API_URL', 'http://localhost:5000/api'),
'app_url' => env('APP_URL', 'https://admin20.qureka.com'),
'app_pub_key' => env('API_SERVER_PUB_KEY'),
'app_server_url' => env('API_SERVER_URL'),
'qureka_old_db' => 'oldmysql',
'qureka_intl_db' => 'intlmysql',
'contest_block_period' => '180 seconds',

'language' => array(
                        'EN'=> 'English',
                        'HN' => 'Hindi'
                    ),

'tags' => array(
                        'IN'=> 'India',
                        'INT' => 'International',
                        'ALL' => 'All'
                    ),
'optimize_mail_to'=>'<EMAIL>',
'optimize_mail_cc'=>'<EMAIL>,<EMAIL>',
'TRACKING_REDIS'=>['host'=>env('TREDIS_HOST'),'password'=>env('TREDIS_PASSWORD'),'port'=>env('TREDIS_PORT')],
'REDIS_CONF'=>['host'=>env('REDIS_HOST'),'password'=>env('REDIS_PASSWORD'),'port'=>env('REDIS_PORT')],
'quiz_reward_ad_placement'=>5,
'apis'=>[
    'signup'=>API_SERVER_URL.'/auth/register',
    'getrole'=>API_SERVER_URL.'/jobroles',
    'start_chat'=>API_SERVER_URL.'/quiz/start_chat',
    'save_profile'=>API_SERVER_URL.'/user_profiles',
    'save_answer'=>API_SERVER_URL.'/quiz/answer',
    'save_mcq'=>API_SERVER_URL.'/quiz/mcq',
    'save_coding'=>API_SERVER_URL.'/quiz/coding',
    'assessment_report'=>API_SERVER_URL.'/generate-report',
    'public_report'=>API_SERVER_URL.'/public-report',
    'send_otp'=>API_SERVER_URL.'/user_profiles/send-otp',
    'verify_otp'=>API_SERVER_URL.'/user_profiles/verify-otp',
    'submit_feedback'=>API_SERVER_URL.'/submit-feedback'
],
'question_messages'=>[
    'Only 30% of participants answered the previous question the way you did. Let’s see how you approach the next one.',
    'You’re moving faster than 60% of other participants',
    'This next one trips up even seasoned experts.',
    'The next question challenges your intuition.',
    'Interesting perspective! Let’s see how you tackle the next challenge.',
    'Good thinking, next one coming up.',
    'Nice start! Most people need a moment to think about that one. Here’s the next challenge.',
    'Your choices so far are shaping up an interesting pattern, ready for the next?',
    'Most participants hesitated at that question, but you moved quickly.',
    'You’re thinking differently from the majority of participants, ready for the next?',
    'Few participants take that approach. Let’s explore your reasoning further.',
    'Here comes a question that challenges common assumptions.',
    'This one separates intuitive thinkers from analytical ones.',
    'Interesting perspective, let’s build on it.',
    'Your answer adds a unique angle to this assessment.',
    'Good thinking! Here’s another to consider.'
],
'skip_messages'=>[
    'No worries, let’s move on to the next one.',
    'Hmm… let’s see if you can try the next question.',
    'That one can be tricky … let’s try another.',
    'Okay, let’s skip it and keep the momentum going.'
    ]
];
//$api_url =  \Config::get('mgl.api_url');
?>



