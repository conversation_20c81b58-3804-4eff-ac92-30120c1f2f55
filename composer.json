{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2.0", "guzzlehttp/guzzle": "^7.8", "laravel/framework": "^11.0", "laravel/helpers": "^1.7", "laravel/passport": "^12.2", "laravel/socialite": "^5.14", "laravel/tinker": "^2.9", "laravel/ui": "^4.5", "lcobucci/jwt": "5.3", "maatwebsite/excel": "^3.1", "predis/predis": "^2.2", "solarium/solarium": "^6.0"}, "require-dev": {"fakerphp/faker": "^1.23", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.1"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "app/modules"}, "files": ["app/Helpers/utility_helper.php", "app/Helpers/ParallelCurl.php", "app/Helpers/app_helper.php"], "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}